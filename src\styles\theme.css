/* Theme Colors */
:root {
  /* Primary Colors */
  --primary-yellow: #F2BC00;
  --primary-blue: #37A7DF;
  --primary-gray: #B7B7B7;
  --primary-light-green: #EEF9F5;
  --primary-green: #248E39;
  --primary-navy: #000080;
  --primary-dark: #1D1D1B;
  --primary-cream: #F6F4EE;

  /* Functional Colors */
  --text-primary: #1D1D1B;
  --text-secondary: #555555;
  --text-light: #FFFFFF;
  --background-light: #F6F4EE;
  --background-white: #FFFFFF;
  --border-color: #E0E0E0;
  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Component-specific colors */
  --header-bg: var(--background-white);
  --sidebar-bg: var(--background-white);
  --sidebar-active: var(--primary-yellow);
  --card-header-bg: var(--primary-blue);
  --card-bg: var(--background-white);
  --button-primary-bg: var(--primary-blue);
  --button-success-bg: var(--primary-green);
  --button-warning-bg: var(--primary-yellow);
  --table-header-bg: var(--primary-light-green);
  --table-row-hover: rgba(55, 167, 223, 0.1);
}

/* Typography */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-primary);
  background-color: var(--background-light);
}
  

.card {
  border-radius: 16px !important;
}

.icon-wrapper {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}


h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
}

/* Card Styles */
.card {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 4px 6px var(--shadow-color);
  border: none;
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  background-color: var(--card-header-bg);
  color: var(--text-light);
  border-bottom: none;
  padding: 15px 20px;
  font-weight: 600;
}

.card-body {
  padding: 20px;
}

/* Button Styles */
.btn-primary {
  background-color: var(--button-primary-bg);
  border-color: var(--button-primary-bg);
  color: var(--text-light);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #2b8bc0;
  border-color: #2b8bc0;
}

.btn-success {
  background-color: var(--button-success-bg);
  border-color: var(--button-success-bg);
}

.btn-success:hover, .btn-success:focus {
  background-color: #1c7230;
  border-color: #1c7230;
}

.btn-warning {
  background-color: var(--button-warning-bg);
  border-color: var(--button-warning-bg);
  color: var(--text-primary);
}

.btn-warning:hover, .btn-warning:focus {
  background-color: #d9a800;
  border-color: #d9a800;
}

/* Table Styles */
.table thead th {
  background-color: var(--table-header-bg);
  color: var(--text-primary);
  border-bottom: 2px solid var(--primary-blue);
  font-weight: 600;
}

.table tbody tr:hover {
  background-color: var(--table-row-hover);
}

/* Form Styles */
.form-control:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 0.2rem rgba(55, 167, 223, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
}

/* Custom Badge Styles */
.badge-primary {
  background-color: var(--primary-blue);
  color: var(--text-light);
}

.badge-success {
  background-color: var(--primary-green);
  color: var(--text-light);
}

.badge-warning {
  background-color: var(--primary-yellow);
  color: var(--text-primary);
}

/* Dashboard Widget Styles */
.widget-stat {
  border-radius: 8px;
  overflow: hidden;
}

.widget-stat.primary {
  background-color: var(--primary-blue);
}

.widget-stat.success {
  background-color: var(--primary-green);
}

.widget-stat.warning {
  background-color: var(--primary-yellow);
}

.widget-stat.navy {
  background-color: var(--primary-navy);
}

/* Sidebar Styles */
.dlabnav {
  background-color: var(--sidebar-bg);
}

.dlabnav .metismenu > li > a {
  color: var(--primary-dark);
}

.dlabnav .metismenu > li:hover > a,
.dlabnav .metismenu > li:focus > a,
.dlabnav .metismenu > li.mm-active > a {
  color: var(--primary-blue);
  background-color: rgba(55, 167, 223, 0.1);
}

/* Sidebar submenu links */
.dlabnav a, .dlabnav .metismenu a {
  color: var(--primary-dark) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  border-radius: 4px !important;
  position: relative !important;
}

.dlabnav a:hover, .dlabnav .metismenu a:hover {
  background-color: rgba(55, 167, 223, 0.1) !important;
  color: var(--primary-blue) !important;
  padding-left: 20px !important;
}

.dlabnav a:hover::before, .dlabnav .metismenu a:hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--primary-blue);
}

/* Sidebar icons */
.dlabnav .la {
  color: var(--primary-blue);
}

.dlabnav .mm-active .la {
  color: var(--primary-yellow);
}

.dlabnav .mm-active > div > span {
  color: var(--primary-yellow) !important;
}

/* Dropdown menu */
.dropdown-menu {
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.dropdown-item {
  padding: 10px 20px;
  transition: all 0.2s ease;
}

.dropdown-item:hover, .dropdown-item:focus {
  background-color: rgba(55, 167, 223, 0.1);
  color: var(--primary-blue);
}

/* Header Styles */
.header {
  background-color: var(--header-bg);
  color: var(--primary-dark);
  border-bottom: 1px solid var(--border-color);
}

.header .dropdown-menu {
  background-color: var(--background-white);
  box-shadow: 0 4px 12px var(--shadow-color);
  border: none;
}

.header .dropdown-item:hover {
  background-color: rgba(55, 167, 223, 0.1);
  color: var(--primary-blue);
}

.header .nav-link {
  color: var(--primary-dark);
}

.header .nav-link:hover {
  color: var(--primary-blue);
}

/* Pagination Styles */
.pagination .page-item.active .page-link {
  background-color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.pagination .page-link {
  color: var(--primary-blue);
}

.pagination .page-link:hover {
  background-color: var(--primary-light-green);
}

/* Alert Styles */
.alert-primary {
  background-color: rgba(55, 167, 223, 0.2);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
}

.alert-success {
  background-color: rgba(36, 142, 57, 0.2);
  border-color: var(--primary-green);
  color: var(--primary-green);
}

.alert-warning {
  background-color: rgba(242, 188, 0, 0.2);
  border-color: var(--primary-yellow);
  color: var(--text-primary);
}
