{"name": "a_e_l", "version": "0.1.0", "private": true, "dependencies": {"@100mslive/react-sdk": "^0.10.33", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.0.2", "@react-keycloak/web": "^3.4.0", "@tailwindcss/vite": "^4.0.6", "@testing-library/react": "^16.2.0", "axios": "^0.21.4", "bootstrap": "^5.3.3", "browserify-zlib": "^0.2.0", "chart.js": "^4.4.8", "clsx": "^2.1.1", "cra-template": "1.2.0", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "https-browserify": "^1.0.0", "react": "^18.3.1", "react-bootstrap": "^2.10.8", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-paginate": "^8.3.0", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "react-select": "^5.10.1", "recharts": "^2.15.3", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "sweetalert2": "^11.19.1", "tailwindcss": "^4.0.6", "url": "^0.11.4", "util": "^0.12.5", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "react-redux": "^9.2.0", "redux": "^5.0.1"}}