# 🥧 StatisticsPieChart - Documentation

## Vue d'ensemble

Le composant `StatisticsPieChart` est un graphique en secteurs (pie chart) interactif qui affiche la répartition des différentes entités du système éducatif : cours, abonnements, niveaux et matières.

## 🎯 Fonctionnalités

### Principales
- **Graphique interactif** avec animations fluides
- **Tooltips personnalisés** avec informations détaillées
- **Légende dynamique** avec icônes et couleurs
- **Statistiques détaillées** sous forme de cartes
- **Résumé global** avec métriques clés
- **Gestion d'erreurs** robuste avec possibilité de réessai

### Design
- **Palette de couleurs cohérente** avec le design system
- **Design responsive** s'adaptant à tous les écrans
- **Animations et transitions** pour une UX fluide
- **États visuels** pour chargement, erreur et données vides

## 📊 Données Affichées

Le composant récupère et affiche les statistiques suivantes :

| Entité | Icône | Couleur | Description |
|--------|-------|---------|-------------|
| **Cours** | 🎓 | #37A7DF (Bleu) | Nombre total de cours disponibles |
| **Abonnements** | 💳 | #F2BC00 (Jaune) | Types d'abonnements proposés |
| **Niveaux** | 📊 | #248E39 (Vert) | Niveaux d'étude disponibles |
| **Matières** | 📚 | #000080 (Bleu foncé) | Matières enseignées |

## 🚀 Installation et Utilisation

### Prérequis
```bash
npm install recharts react
```

### Import et utilisation
```jsx
import StatisticsPieChart from './components/dashboard/StatisticsPieChart';

function Dashboard() {
  return (
    <div className="container">
      <div className="row">
        <div className="col-12">
          <StatisticsPieChart />
        </div>
      </div>
    </div>
  );
}
```

### Intégration avec d'autres graphiques
```jsx
import MatieresParAbonnementChart from './MatieresParAbonnementChart';
import StatisticsPieChart from './StatisticsPieChart';

function CompleteDashboard() {
  return (
    <div className="row">
      <div className="col-lg-6">
        <MatieresParAbonnementChart />
      </div>
      <div className="col-lg-6">
        <StatisticsPieChart />
      </div>
    </div>
  );
}
```

## 🔧 Configuration

### Endpoint API
Le composant utilise l'endpoint : `/api/statistics`

**Réponse attendue :**
```json
{
  "totalCours": 25,
  "totalAbonnements": 3,
  "totalNiveaux": 5,
  "totalMatiere": 12
}
```

### Personnalisation des couleurs
```javascript
const CHART_COLORS = {
  COURS: "#37A7DF",        // Bleu principal
  ABONNEMENTS: "#F2BC00",  // Jaune/Orange
  NIVEAUX: "#248E39",      // Vert
  MATIERES: "#000080",     // Bleu foncé
  BACKGROUND: "#F6F4EE",   // Crème
  LIGHT_BG: "#EEF9F5",    // Vert très clair
  TEXT_DARK: "#1D1D1B",   // Noir/Gris foncé
  NEUTRAL: "#B7B7B7"      // Gris
};
```

## 📱 Responsive Design

Le composant s'adapte automatiquement aux différentes tailles d'écran :

- **Desktop** : Graphique pleine largeur avec statistiques en grille
- **Tablet** : Adaptation des proportions et espacement
- **Mobile** : Empilement vertical des éléments

## 🎨 Personnalisation Avancée

### Modifier les icônes
```javascript
const chartData = [
  {
    name: "Cours",
    value: stats.totalCours,
    icon: "🎓", // Changez ici
    description: "Cours disponibles"
  },
  // ...
];
```

### Ajuster les dimensions
```jsx
<ResponsiveContainer width="100%" height={400}>
  {/* Changez height selon vos besoins */}
</ResponsiveContainer>
```

### Personnaliser les tooltips
```javascript
const CustomTooltip = ({ active, payload }) => {
  // Votre logique personnalisée ici
  return (
    <div style={{ /* vos styles */ }}>
      {/* Votre contenu personnalisé */}
    </div>
  );
};
```

## 🐛 Gestion d'Erreurs

Le composant gère automatiquement :

- **Erreurs réseau** : Affichage d'un message avec bouton de réessai
- **Données manquantes** : Affichage d'un état vide informatif
- **Données nulles** : Filtrage et affichage approprié
- **Timeout API** : Gestion des délais d'attente

## 📈 Métriques Affichées

### Graphique principal
- **Secteurs colorés** proportionnels aux valeurs
- **Labels en pourcentage** sur les secteurs significatifs
- **Animation d'entrée** fluide

### Cartes statistiques
- **Valeur numérique** mise en évidence
- **Pourcentage du total** calculé automatiquement
- **Description** contextuelle
- **Couleur** correspondant au secteur

### Résumé global
- **Types d'entités** : Nombre de catégories
- **Total entités** : Somme de toutes les valeurs
- **Moyenne/type** : Répartition moyenne

## 🔍 Débogage

### Logs de développement
Le composant affiche des logs détaillés en mode développement :

```javascript
console.log("📊 Statistics API Response:", response.data);
console.log("✅ Formatted chart data:", filteredData);
```

### Vérification des données
```javascript
// Vérifiez que l'API retourne les bonnes données
const expectedFormat = {
  totalCours: Number,
  totalAbonnements: Number,
  totalNiveaux: Number,
  totalMatiere: Number
};
```

## 🚀 Performance

### Optimisations incluses
- **Filtrage des données nulles** pour un rendu plus rapide
- **Animations optimisées** avec Recharts
- **Rendu conditionnel** selon l'état des données
- **Memoization** des calculs de pourcentage

### Bonnes pratiques
- Utilisez `React.memo()` si le composant parent se re-rend souvent
- Implémentez un cache pour les données API si nécessaire
- Considérez le lazy loading pour les gros dashboards

## 📝 Exemples d'Usage

### Dashboard simple
```jsx
<StatisticsPieChart />
```

### Dashboard avec titre personnalisé
```jsx
<div className="dashboard-section">
  <h2>Répartition des Ressources</h2>
  <StatisticsPieChart />
</div>
```

### Dashboard avec gestion d'état parent
```jsx
function Dashboard() {
  const [refreshKey, setRefreshKey] = useState(0);
  
  return (
    <div>
      <button onClick={() => setRefreshKey(prev => prev + 1)}>
        Actualiser
      </button>
      <StatisticsPieChart key={refreshKey} />
    </div>
  );
}
```

## 🤝 Contribution

Pour contribuer au composant :

1. **Fork** le projet
2. **Créez** une branche pour votre fonctionnalité
3. **Testez** vos modifications
4. **Documentez** les changements
5. **Soumettez** une pull request

## 📄 Licence

Ce composant fait partie du système éducatif ThinkTrend et suit la même licence que le projet principal.
