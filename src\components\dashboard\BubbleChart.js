/**
 * ========================================================================
 * COMPOSANT GRAPHIQUE À BULLES (BUBBLE CHART) - SYSTÈME ÉDUCATIF
 * ========================================================================
 *
 * DESCRIPTION :
 * Ce composant affiche un graphique à bulles interactif qui présente
 * la répartition des matières, niveaux et étudiants du système éducatif.
 * Chaque bulle représente une entité avec une taille proportionnelle à sa valeur.
 *
 * FONCTIONNALITÉS PRINCIPALES :
 * ✅ Récupération automatique des statistiques via l'API REST
 * ✅ Graphique à bulles interactif avec animations fluides
 * ✅ Tooltips informatifs avec détails complets
 * ✅ Légende dynamique avec icônes thématiques
 * ✅ Cartes statistiques détaillées sous le graphique
 * ✅ Gestion complète des états (chargement, erreur, données vides)
 * ✅ Design responsive s'adaptant à tous les écrans
 * ✅ Palette de couleurs conforme au design system
 *
 * DONNÉES AFFICHÉES :
 * 📚 Matières - Nombre total de matières enseignées (Couleur: #37A7DF - Bleu)
 * 📊 Niveaux - Niveaux d'étude disponibles (Couleur: #248E39 - Vert)
 * 👥 Étudiants - Nombre total d'étudiants inscrits (Couleur: #F2BC00 - Jaune)
 *
 * STRUCTURE DU COMPOSANT :
 * 1. États React (data, loading, error, maxValue)
 * 2. Hook useEffect pour la récupération des données
 * 3. Fonction CustomTooltip pour les infobulles
 * 4. Fonction de calcul de taille des bulles
 * 5. Rendu conditionnel selon l'état
 * 6. Graphique principal avec Recharts
 * 7. Cartes statistiques résumées
 *
 * UTILISATION :
 * import BubbleChart from './components/dashboard/BubbleChart';
 *
 * function Dashboard() {
 *   return (
 *     <div className="col-lg-6">
 *       <BubbleChart />
 *     </div>
 *   );
 * }
 *
 * PRÉREQUIS TECHNIQUES :
 * - Bibliothèque recharts installée (npm install recharts)
 * - Service axiosInstance configuré pour les appels API
 * - Endpoints API /api/statistics et /api/etudiants/count disponibles
 * - Icônes FontAwesome chargées dans l'application
 * - Bootstrap pour les classes CSS (spinner, alert, etc.)
 *
 * FORMAT DE DONNÉES API ATTENDU :
 * /api/statistics: {
 *   "totalMatiere": 12,
 *   "totalNiveaux": 5
 * }
 * /api/etudiants/count: {
 *   "totalEtudiants": 150
 * }
 *
 * <AUTHOR> de développement ThinkTrend
 * @version 1.0
 * @since 2025-01-24
 * @lastModified 2025-01-24
 * ========================================================================
 */

import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import {
  ScatterChart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Cell,
} from "recharts";

/**
 * PALETTE DE COULEURS POUR LES BULLES DU GRAPHIQUE
 *
 * Chaque couleur correspond à une entité spécifique du système :
 * - Index 0 (#37A7DF) : Matières (Bleu principal)
 * - Index 1 (#248E39) : Niveaux (Vert)
 * - Index 2 (#F2BC00) : Étudiants (Jaune/Orange)
 *
 * Ces couleurs sont conformes au design system de l'application
 * et garantissent une bonne lisibilité et accessibilité.
 */
const BUBBLE_COLORS = ["#37A7DF", "#248E39", "#F2BC00"];

/**
 * CONFIGURATION DES COULEURS DU THÈME
 *
 * Définit les couleurs utilisées pour les éléments d'interface :
 * - BACKGROUND : Couleur de fond principal des conteneurs
 * - LIGHT_BG : Couleur de fond claire pour les zones d'accent
 * - TEXT_DARK : Couleur du texte principal (contraste élevé)
 * - NEUTRAL : Couleur pour les éléments secondaires et bordures
 * - PRIMARY : Couleur principale de la marque (utilisée pour les accents)
 */
const THEME_COLORS = {
  BACKGROUND: "#F6F4EE",    // Crème - Fond principal
  LIGHT_BG: "#EEF9F5",     // Vert très clair - Zones d'accent
  TEXT_DARK: "#1D1D1B",    // Noir/Gris foncé - Texte principal
  NEUTRAL: "#B7B7B7",      // Gris - Éléments secondaires
  PRIMARY: "#37A7DF"       // Bleu - Couleur de marque
};

/**
 * COMPOSANT PRINCIPAL BUBBLECHART
 *
 * Ce composant gère l'affichage complet du graphique à bulles,
 * incluant la récupération des données, la gestion des états,
 * et le rendu conditionnel selon la situation.
 *
 * @returns {JSX.Element} Le composant React complet avec graphique et statistiques
 */
const BubbleChart = () => {
  // ====================================================================
  // ÉTATS DU COMPOSANT
  // ====================================================================

  /**
   * Données formatées pour le graphique à bulles
   * Structure : [{ name: string, x: number, y: number, z: number, icon: string, color: string }]
   */
  const [data, setData] = useState([]);

  /**
   * État de chargement des données depuis l'API
   * true = en cours de chargement, false = chargement terminé
   */
  const [loading, setLoading] = useState(true);

  /**
   * Message d'erreur en cas d'échec de récupération des données
   * null = pas d'erreur, string = message d'erreur à afficher
   */
  const [error, setError] = useState(null);

  /**
   * Valeur maximale pour normaliser la taille des bulles
   * Utilisé pour calculer la taille proportionnelle de chaque bulle
   */
  const [maxValue, setMaxValue] = useState(0);

  // ====================================================================
  // RÉCUPÉRATION DES DONNÉES
  // ====================================================================

  /**
   * Hook useEffect pour charger les données au montage du composant
   * Se déclenche une seule fois lors du premier rendu (dépendance vide [])
   */
  useEffect(() => {
    /**
     * FONCTION ASYNCHRONE DE RÉCUPÉRATION DES STATISTIQUES
     *
     * Cette fonction :
     * 1. Appelle les APIs /api/statistics et /api/etudiants/count
     * 2. Transforme les données au format requis par le graphique à bulles
     * 3. Calcule les positions et tailles des bulles
     * 4. Met à jour les états du composant
     * 5. Gère les erreurs et les états de chargement
     */
    const fetchBubbleData = async () => {
      try {
        // Initialisation de l'état de chargement
        setLoading(true);
        setError(null);

        // ============================================================
        // APPELS API PARALLÈLES POUR RÉCUPÉRER LES DONNÉES
        // ============================================================
        const [statsResponse, etudiantsResponse] = await Promise.all([
          axiosInstance.get("/api/statistics"),
          axiosInstance.get("/api/etudiants/count").catch(() => ({ data: { totalEtudiants: 0 } }))
        ]);

        console.log("📊 Données statistiques reçues:", statsResponse.data);
        console.log("👥 Données étudiants reçues:", etudiantsResponse.data);

        const stats = statsResponse.data;
        const etudiantsData = etudiantsResponse.data;

        // ============================================================
        // TRANSFORMATION DES DONNÉES POUR LE GRAPHIQUE À BULLES
        // ============================================================
        const bubbleData = [
          {
            name: "Matières",
            x: 1,                                    // Position X fixe
            y: 1,                                    // Position Y fixe
            z: stats.totalMatiere || 0,              // Taille de la bulle
            value: stats.totalMatiere || 0,          // Valeur pour affichage
            icon: "📚",                              // Icône thématique
            color: BUBBLE_COLORS[0],                 // Couleur bleue
            description: "Matières enseignées"
          },
          {
            name: "Niveaux",
            x: 2,                                    // Position X fixe
            y: 1,                                    // Position Y fixe
            z: stats.totalNiveaux || 0,              // Taille de la bulle
            value: stats.totalNiveaux || 0,          // Valeur pour affichage
            icon: "📊",                              // Icône thématique
            color: BUBBLE_COLORS[1],                 // Couleur verte
            description: "Niveaux d'étude"
          },
          {
            name: "Étudiants",
            x: 3,                                    // Position X fixe
            y: 1,                                    // Position Y fixe
            z: etudiantsData.totalEtudiants || 0,    // Taille de la bulle
            value: etudiantsData.totalEtudiants || 0, // Valeur pour affichage
            icon: "👥",                              // Icône thématique
            color: BUBBLE_COLORS[2],                 // Couleur jaune
            description: "Étudiants inscrits"
          }
        ];

        // ============================================================
        // CALCULS POUR LA NORMALISATION DES BULLES
        // ============================================================
        // Trouver la valeur maximale pour normaliser les tailles
        const maxVal = Math.max(...bubbleData.map(item => item.z));

        // Normaliser les tailles des bulles (minimum 20, maximum 100)
        const normalizedData = bubbleData.map(item => ({
          ...item,
          z: maxVal > 0 ? Math.max(20, (item.z / maxVal) * 100) : 20
        }));

        // ============================================================
        // MISE À JOUR DES ÉTATS
        // ============================================================
        setData(normalizedData);
        setMaxValue(maxVal);

        console.log("✅ Données formatées pour le graphique à bulles:", normalizedData);

      } catch (err) {
        // ============================================================
        // GESTION DES ERREURS
        // ============================================================
        console.error("❌ Erreur lors du chargement des données:", err);

        // Définir un message d'erreur utilisateur-friendly
        const errorMessage = err.response?.data?.message ||
                           err.message ||
                           "Erreur lors du chargement des données du graphique";
        setError(errorMessage);

      } finally {
        // ============================================================
        // FINALISATION
        // ============================================================
        // Arrêter l'état de chargement dans tous les cas
        setLoading(false);
      }
    };

    // Exécuter la fonction de récupération des données
    fetchBubbleData();
  }, []); // Dépendance vide = exécution uniquement au montage

  // ====================================================================
  // COMPOSANTS PERSONNALISÉS
  // ====================================================================

  /**
   * COMPOSANT TOOLTIP PERSONNALISÉ
   *
   * Affiche une infobulle élégante lors du survol des bulles du graphique.
   * Contient :
   * - L'icône et le nom de l'entité
   * - La valeur numérique
   * - La description de l'entité
   * - Le pourcentage par rapport au total (si applicable)
   *
   * @param {boolean} active - Indique si le tooltip est actif (survol)
   * @param {Array} payload - Données de la bulle survolée
   * @returns {JSX.Element|null} Le tooltip ou null si inactif
   */
  const CustomTooltip = ({ active, payload }) => {
    // Vérifier si le tooltip doit être affiché
    if (active && payload && payload.length) {
      const data = payload[0].payload;

      return (
        <div style={{
          backgroundColor: THEME_COLORS.BACKGROUND,    // Fond crème
          border: `2px solid ${data.color}`,           // Bordure colorée selon l'entité
          borderRadius: "10px",                        // Coins arrondis
          padding: "15px",                             // Espacement interne
          boxShadow: "0 6px 20px rgba(0,0,0,0.15)",   // Ombre portée
          fontSize: "14px",                            // Taille de police
          minWidth: "200px"                            // Largeur minimale
        }}>
          {/* Ligne 1 : Icône + Nom de l'entité */}
          <div style={{
            display: "flex",
            alignItems: "center",
            marginBottom: "8px"
          }}>
            <span style={{ fontSize: "24px", marginRight: "8px" }}>
              {data.icon}
            </span>
            <span style={{
              fontWeight: "700",
              color: THEME_COLORS.TEXT_DARK,
              fontSize: "16px"
            }}>
              {data.name}
            </span>
          </div>

          {/* Ligne 2 : Valeur numérique */}
          <div style={{
            fontSize: "20px",
            fontWeight: "bold",
            color: data.color,
            marginBottom: "5px"
          }}>
            {data.value}
          </div>

          {/* Ligne 3 : Description */}
          <div style={{
            fontSize: "12px",
            color: THEME_COLORS.NEUTRAL,
            fontStyle: "italic"
          }}>
            {data.description}
          </div>

          {/* Ligne 4 : Taille de la bulle (info technique) */}
          <div style={{
            fontSize: "10px",
            color: THEME_COLORS.NEUTRAL,
            marginTop: "8px",
            borderTop: `1px solid ${THEME_COLORS.NEUTRAL}`,
            paddingTop: "5px"
          }}>
            Taille de bulle: {Math.round(data.z)}
          </div>
        </div>
      );
    }

    // Retourner null si le tooltip ne doit pas être affiché
    return null;
  };

  // ====================================================================
  // RENDU CONDITIONNEL SELON L'ÉTAT
  // ====================================================================

  /**
   * RENDU DE L'ÉTAT DE CHARGEMENT
   *
   * Affiché pendant la récupération des données depuis les APIs.
   * Contient un spinner animé et un message informatif.
   *
   * @returns {JSX.Element} Interface de chargement
   */
  if (loading) {
    return (
      <div style={{
        padding: "20px",
        textAlign: "center",
        backgroundColor: THEME_COLORS.BACKGROUND,
        borderRadius: "15px",
        boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
      }}>
        <h5 style={{ color: THEME_COLORS.PRIMARY, marginBottom: "20px" }}>
          🫧 Graphique à Bulles - Entités du Système
        </h5>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "200px",
          color: THEME_COLORS.PRIMARY
        }}>
          <div className="spinner-border me-2" role="status" aria-hidden="true"></div>
          Chargement des données...
        </div>
      </div>
    );
  }

  /**
   * RENDU DE L'ÉTAT D'ERREUR
   *
   * Affiché en cas d'échec de récupération des données.
   * Contient le message d'erreur et un bouton pour réessayer.
   *
   * @returns {JSX.Element} Interface d'erreur avec possibilité de réessai
   */
  if (error) {
    return (
      <div style={{
        padding: "20px",
        backgroundColor: THEME_COLORS.BACKGROUND,
        borderRadius: "15px",
        boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
      }}>
        <h5 style={{ color: THEME_COLORS.PRIMARY, marginBottom: "20px" }}>
          🫧 Graphique à Bulles - Entités du Système
        </h5>
        <div className="alert alert-danger" role="alert" style={{
          backgroundColor: "#fff",
          borderLeft: "4px solid #dc3545",
          borderRadius: "0 4px 4px 0"
        }}>
          <i className="fa fa-exclamation-triangle me-2"></i>
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => window.location.reload()}
          style={{ backgroundColor: THEME_COLORS.PRIMARY, borderColor: THEME_COLORS.PRIMARY }}
        >
          <i className="fa fa-refresh me-2"></i>
          Réessayer
        </button>
      </div>
    );
  }

  /**
   * RENDU PRINCIPAL DU COMPOSANT
   *
   * Affiché lorsque les données sont chargées avec succès.
   * Contient :
   * 1. En-tête avec titre et informations générales
   * 2. Message informatif si aucune donnée
   * 3. Graphique à bulles interactif
   * 4. Cartes statistiques détaillées
   * 5. État vide si aucune donnée disponible
   *
   * @returns {JSX.Element} Interface complète du graphique
   */
  return (
    <div style={{
      backgroundColor: THEME_COLORS.BACKGROUND,
      borderRadius: "15px",
      padding: "25px",
      boxShadow: "0 8px 25px rgba(0,0,0,0.12)",
      border: `1px solid ${THEME_COLORS.LIGHT_BG}`,
      position: "relative",
      overflow: "hidden"
    }}>
      {/* Décoration d'arrière-plan */}
      <div style={{
        position: "absolute",
        top: "-40px",
        right: "-40px",
        width: "150px",
        height: "150px",
        background: `linear-gradient(135deg, ${BUBBLE_COLORS[0]}20, ${BUBBLE_COLORS[2]}20)`,
        borderRadius: "50%",
        zIndex: 0
      }}></div>

      <div style={{ position: "relative", zIndex: 1 }}>
        {/* En-tête du graphique */}
        <div style={{
          textAlign: "center",
          marginBottom: "25px",
          padding: "15px",
          backgroundColor: THEME_COLORS.LIGHT_BG,
          borderRadius: "10px",
          border: `2px solid ${THEME_COLORS.PRIMARY}`
        }}>
          <h5 style={{
            color: THEME_COLORS.PRIMARY,
            marginBottom: "8px",
            fontWeight: "700",
            fontSize: "20px",
            textShadow: "0 1px 2px rgba(0,0,0,0.1)"
          }}>
            🫧 Graphique à Bulles - Entités du Système
          </h5>
          <p style={{
            color: THEME_COLORS.TEXT_DARK,
            margin: "0",
            fontSize: "14px",
            fontStyle: "italic"
          }}>
            Visualisation comparative des matières, niveaux et étudiants
          </p>
          {maxValue > 0 && (
            <div style={{
              marginTop: "10px",
              fontSize: "16px",
              fontWeight: "600",
              color: THEME_COLORS.TEXT_DARK
            }}>
              Valeur maximale: {maxValue}
            </div>
          )}
        </div>

        {data.length > 0 ? (
          <>
            {/* Message informatif si toutes les valeurs sont à 0 */}
            {maxValue === 0 && (
              <div className="alert alert-info" style={{
                backgroundColor: THEME_COLORS.LIGHT_BG,
                borderLeft: `4px solid ${THEME_COLORS.PRIMARY}`,
                borderRadius: "0 4px 4px 0",
                marginBottom: "20px"
              }}>
                <i className="fa fa-info-circle me-2" style={{ color: THEME_COLORS.PRIMARY }}></i>
                <strong>Information :</strong> Le système ne contient pas encore de données.
                <br />
                <small>
                  Commencez par ajouter des matières, niveaux et étudiants
                  pour voir les bulles dans ce graphique.
                </small>
              </div>
            )}

            {/* Graphique à bulles */}
            <ResponsiveContainer width="100%" height={400}>
              <ScatterChart
                margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke={THEME_COLORS.NEUTRAL}
                  opacity={0.3}
                />
                <XAxis
                  type="number"
                  dataKey="x"
                  domain={[0.5, 3.5]}
                  tick={false}
                  axisLine={false}
                  tickLine={false}
                />
                <YAxis
                  type="number"
                  dataKey="y"
                  domain={[0.5, 1.5]}
                  tick={false}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  formatter={(value, entry) => (
                    <span style={{
                      color: THEME_COLORS.TEXT_DARK,
                      fontWeight: "500",
                      fontSize: "14px"
                    }}>
                      {entry.payload?.icon} {value}
                    </span>
                  )}
                />
                <Scatter data={data} fill="#8884d8">
                  {data.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={entry.color}
                      stroke={THEME_COLORS.BACKGROUND}
                      strokeWidth={3}
                    />
                  ))}
                </Scatter>
              </ScatterChart>
            </ResponsiveContainer>

            {/* Cartes statistiques détaillées */}
            <div style={{
              marginTop: "20px",
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "15px"
            }}>
              {data.map((item, index) => (
                <div
                  key={item.name}
                  style={{
                    padding: "15px",
                    backgroundColor: THEME_COLORS.LIGHT_BG,
                    borderRadius: "10px",
                    border: `2px solid ${item.color}`,
                    textAlign: "center",
                    boxShadow: `0 2px 8px ${item.color}30`,
                    transition: "all 0.3s ease",
                    cursor: "pointer"
                  }}
                  onMouseOver={(e) => e.currentTarget.style.transform = "translateY(-3px)"}
                  onMouseOut={(e) => e.currentTarget.style.transform = "translateY(0)"}
                >
                  <div style={{
                    fontSize: "28px",
                    marginBottom: "8px"
                  }}>
                    {item.icon}
                  </div>
                  <div style={{
                    fontSize: "32px",
                    fontWeight: "bold",
                    color: item.color,
                    marginBottom: "5px"
                  }}>
                    {item.value}
                  </div>
                  <div style={{
                    fontSize: "14px",
                    fontWeight: "600",
                    color: THEME_COLORS.TEXT_DARK,
                    marginBottom: "3px"
                  }}>
                    {item.name}
                  </div>
                  <div style={{
                    fontSize: "12px",
                    color: THEME_COLORS.NEUTRAL,
                    fontStyle: "italic"
                  }}>
                    {item.description}
                  </div>
                  <div style={{
                    fontSize: "10px",
                    color: THEME_COLORS.NEUTRAL,
                    marginTop: "5px",
                    fontWeight: "500"
                  }}>
                    Taille bulle: {Math.round(item.z)}
                  </div>
                </div>
              ))}
            </div>

            {/* Résumé global */}
            <div style={{
              marginTop: "20px",
              padding: "20px",
              background: `linear-gradient(135deg, ${THEME_COLORS.LIGHT_BG}, ${THEME_COLORS.BACKGROUND})`,
              borderRadius: "12px",
              border: `1px solid ${THEME_COLORS.NEUTRAL}`,
              textAlign: "center"
            }}>
              <h6 style={{
                color: THEME_COLORS.TEXT_DARK,
                marginBottom: "15px",
                fontWeight: "600"
              }}>
                📊 Résumé du Système Éducatif
              </h6>
              <div style={{
                display: "flex",
                justifyContent: "space-around",
                flexWrap: "wrap",
                gap: "15px"
              }}>
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    fontSize: "24px",
                    fontWeight: "bold",
                    color: BUBBLE_COLORS[0]
                  }}>
                    {data.reduce((sum, item) => sum + item.value, 0)}
                  </div>
                  <small style={{ color: THEME_COLORS.TEXT_DARK }}>
                    Total entités
                  </small>
                </div>
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    fontSize: "24px",
                    fontWeight: "bold",
                    color: BUBBLE_COLORS[1]
                  }}>
                    {data.length}
                  </div>
                  <small style={{ color: THEME_COLORS.TEXT_DARK }}>
                    Types d'entités
                  </small>
                </div>
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    fontSize: "24px",
                    fontWeight: "bold",
                    color: BUBBLE_COLORS[2]
                  }}>
                    {maxValue}
                  </div>
                  <small style={{ color: THEME_COLORS.TEXT_DARK }}>
                    Valeur maximale
                  </small>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div style={{
            textAlign: "center",
            padding: "50px",
            color: THEME_COLORS.NEUTRAL,
            backgroundColor: THEME_COLORS.LIGHT_BG,
            borderRadius: "12px",
            border: `2px dashed ${THEME_COLORS.NEUTRAL}`
          }}>
            <div style={{
              fontSize: "64px",
              marginBottom: "20px",
              color: THEME_COLORS.PRIMARY,
              opacity: 0.6
            }}>
              🫧
            </div>
            <h6 style={{
              margin: "0 0 10px 0",
              fontSize: "18px",
              color: THEME_COLORS.TEXT_DARK,
              fontWeight: "600"
            }}>
              Aucune donnée disponible
            </h6>
            <p style={{
              margin: "0",
              color: THEME_COLORS.NEUTRAL,
              fontSize: "14px"
            }}>
              Les données du système ne sont pas encore disponibles.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// ====================================================================
// EXPORT DU COMPOSANT
// ====================================================================

/**
 * EXPORT PAR DÉFAUT DU COMPOSANT BUBBLECHART
 *
 * Ce composant peut être importé et utilisé dans d'autres parties
 * de l'application pour afficher les statistiques du système éducatif
 * sous forme de graphique à bulles interactif.
 *
 * EXEMPLES D'UTILISATION :
 *
 * 1. Import simple :
 *    import BubbleChart from './components/dashboard/BubbleChart';
 *    <BubbleChart />
 *
 * 2. Dans un dashboard :
 *    <div className="col-lg-6">
 *      <BubbleChart />
 *    </div>
 *
 * 3. Avec gestion d'état parent :
 *    const [refreshKey, setRefreshKey] = useState(0);
 *    <BubbleChart key={refreshKey} />
 *
 * 4. Dans un layout responsive :
 *    <div className="row">
 *      <div className="col-xl-4 col-lg-6 col-12">
 *        <BubbleChart />
 *      </div>
 *    </div>
 *
 * NOTES IMPORTANTES :
 * - Le composant gère automatiquement ses états internes
 * - Aucune prop n'est requise pour le fonctionnement de base
 * - Les données sont récupérées automatiquement depuis les APIs
 * - Le design est responsive et s'adapte à tous les écrans
 * - Les couleurs respectent le design system de l'application
 * - Les bulles sont dimensionnées proportionnellement aux valeurs
 * - Les tooltips fournissent des informations détaillées au survol
 *
 * APIS UTILISÉES :
 * - /api/statistics : Pour les matières et niveaux
 * - /api/etudiants/count : Pour le nombre d'étudiants
 *
 * DÉPENDANCES :
 * - recharts : Pour le graphique ScatterChart
 * - axiosInstance : Pour les appels API
 * - Bootstrap : Pour les classes CSS
 * - FontAwesome : Pour les icônes
 */
export default BubbleChart;
