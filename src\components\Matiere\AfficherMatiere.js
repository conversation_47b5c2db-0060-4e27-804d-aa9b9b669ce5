import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import axios from "axios";
import keycloak from "../../keycloak";
import Select from "react-select";
import "./AfficherMatiere.css"; // Import the CSS file
import "../../styles/theme.css"; // Import theme styles
import "../../styles/responsive.css"; // Import responsive styles

const AfficherMatiere = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [matiereModif, setMatiereModif] = useState(null);
  const [niveaux, setNiveaux] = useState([]);
  const [abonnements, setAbonnements] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [matieres, setMatieres] = useState([]);
  const [showModalDelete, setShowModalDelete] = useState(false);
  const [selectedMatiere, setSelectedMatiere] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [errors, setErrors] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);
  // Add missing state variables for editing
  const [nouveauNom, setNouveauNom] = useState("");
  const [nouvelleDescription, setNouvelleDescription] = useState("");
  const [nouvelleDuree, setNouvelleDuree] = useState("");
  const [nouveauNiveau, setNouveauNiveau] = useState("");
  const [nouveauAbonnement, setNouveauAbonnement] = useState("");
  const [nouvelleImage, setNouvelleImage] = useState(null);
  const [imageUrls, setImageUrls] = useState({});
  const [userId, setUserId] = useState(null);
  const [isEnseignant, setIsEnseignant] = useState(false);
  const [hasAdminRole, setHasAdminRole] = useState(false);
  const [liveSessionRecordings, setLiveSessionRecordings] = useState({});
  const [showRecordingsModal, setShowRecordingsModal] = useState(false);
  const [selectedMatiereRecordings, setSelectedMatiereRecordings] = useState(null);
  const [recordingFile, setRecordingFile] = useState(null);
  const [uploadingRecording, setUploadingRecording] = useState(false);
  const [recordingUploadSuccess, setRecordingUploadSuccess] = useState("");
  const [recordingUploadError, setRecordingUploadError] = useState("");
  const [selectedNiveau, setSelectedNiveau] = useState(null);
  const [filterNiveau, setFilterNiveau] = useState(""); // New state for filtering recordings by niveau
  const [showNiveauSelectModal, setShowNiveauSelectModal] = useState(false); // State for niveau selection modal

  // Check if user is an enseignant and get user ID
  useEffect(() =>{
    if (keycloak.authenticated) {
      const isTeacher = keycloak.hasResourceRole("ENSEIGNANT") || keycloak.hasRealmRole("ENSEIGNANT");
      const isAdmin = keycloak.hasResourceRole("ADMIN") || keycloak.hasRealmRole("ADMIN");
      setIsEnseignant(isTeacher);
      setHasAdminRole(isAdmin);

      try {
        const tokenParsed = keycloak.tokenParsed;
        if (tokenParsed && tokenParsed.preferred_username && !isNaN(tokenParsed.preferred_username)) {
          setUserId(tokenParsed.preferred_username);
        } else if (tokenParsed && tokenParsed.sub) {
          setUserId(tokenParsed.sub);
        }
      } catch (error) {
        console.error("Error parsing token:", error);
      }
    }
  }, []);

  const abonnementsParPage = 8;

  const loadImage = async (imageId) => {
    try {
      if (!imageId) return '/default-image.jpg';

      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: 'arraybuffer'
      });

      const blob = new Blob([response.data], { type: response.headers['content-type'] });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Error loading image:', error);
      return '/default-image.jpg';
    }
  };

  useEffect(() => {
    return () => {
      Object.values(imageUrls).forEach(url => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  // Define fetchData outside useEffect so it can be called from other functions
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log("Fetching data with params:", {
        isEnseignant,
        userId,
        currentPage,
        abonnementsParPage
      });

      const promises = [
        isEnseignant && userId
          ? axiosInstance.get(`/api/enseignants/${userId}/matieres`)
          : axiosInstance.get(`/api/matieres/page?page=${currentPage - 1}&size=${abonnementsParPage}`),
        axiosInstance.get("/api/niveaux/all"),
        axiosInstance.get("/api/abonnements/all"),
      ];

      console.log("API requests initiated");
      const [matieresRes, niveauxRes, abonnementsRes] = await Promise.all(promises);

      console.log("All API responses received");
      console.log("Matières response:", matieresRes.data);
      console.log("Structure of first matière:", matieresRes.data?.content?.[0] || matieresRes.data?.[0]);

      if (matieresRes.data) {
        if (isEnseignant && userId) {
          setMatieres(matieresRes.data || []);
          setTotalPages(
            Math.ceil(matieresRes.data.length / abonnementsParPage) || 1
          );

          // Fetch recordings for each matiere
          const matiereIds = matieresRes.data.map(matiere => matiere.idMatiere);
          for (const matiereId of matiereIds) {
            fetchRecordings(matiereId);
          }
        } else {
          setMatieres(matieresRes.data.content || []);
          setTotalPages(matieresRes.data.totalPages || 1);

          // Fetch recordings for each matiere
          const matiereIds = matieresRes.data.content.map(matiere => matiere.idMatiere);
          for (const matiereId of matiereIds) {
            fetchRecordings(matiereId);
          }
        }

        // Précharger les images
        const imagePromises = matieresRes.data.content?.map(matiere =>
          matiere.image?.idImage ? loadImage(matiere.image.idImage) : Promise.resolve(null)
        ) || [];

        const urls = await Promise.all(imagePromises);
        const newImageUrls = {};
        matieresRes.data.content?.forEach((matiere, index) => {
          if (matiere.image?.idImage && urls[index]) {
            newImageUrls[matiere.image.idImage] = urls[index];
          }
        });
        setImageUrls(newImageUrls);
      }

      if (niveauxRes.data) {
        console.log("Niveaux fetched successfully:", niveauxRes.data);
        setNiveaux(niveauxRes.data);
      } else {
        console.warn("No niveaux data received from API");
      }

      if (abonnementsRes.data) {
        console.log("Abonnements fetched successfully:", abonnementsRes.data);
        setAbonnements(abonnementsRes.data);
      } else {
        console.warn("No abonnements data received from API");
      }
    } catch (err) {
      console.error("Error fetching data:", err);

      // More detailed error logging
      if (err.response) {
        console.error("Response error data:", err.response.data);
        console.error("Response error status:", err.response.status);
        console.error("Response error headers:", err.response.headers);
      } else if (err.request) {
        console.error("Request error - no response received:", err.request);
      } else {
        console.error("Error message:", err.message);
      }

      // Check if error is related to abonnements or niveaux
      if (err.message && err.message.includes("abonnements")) {
        console.error("Error fetching abonnements:", err);
        setError("Erreur lors du chargement des abonnements. Veuillez réessayer.");
      } else if (err.message && err.message.includes("niveaux")) {
        console.error("Error fetching niveaux:", err);
        setError("Erreur lors du chargement des niveaux. Veuillez réessayer.");
      } else {
        setError(err.response?.data?.message || "Erreur lors du chargement des données");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (keycloak.authenticated) {
      fetchData();
    }
  }, [currentPage, isEnseignant, userId]);

  // These variables can be used for client-side pagination if needed
  // const indexOfLastMatiere = currentPage * abonnementsParPage;
  // const indexOfFirstMatiere = indexOfLastMatiere - abonnementsParPage;
  // const matieresAffiches = matieres.slice(indexOfFirstMatiere, indexOfLastMatiere);

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };



  // Filtrage des matiere selon le terme de recherche
  const matieresFiltres = matieres.filter((matiere) => {
    return (
      (matiere.nomMatiere &&
        typeof matiere.nomMatiere === "string" &&
        matiere.nomMatiere.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (matiere.niveau &&
        typeof matiere.niveau === "string" &&
        matiere.niveau.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (matiere.duree &&
        !isNaN(matiere.duree) &&
        matiere.duree.toString().includes(searchTerm)) // Recherche par durée
    );
  });

  // Fonction pour afficher le modal de suppression
  const handleShowDeleteModal = (matiere) => {
    if (matiere && matiere.idMatiere) {
      setSelectedMatiere(matiere);
      setShowModalDelete(true);
    } else {
      console.error("Matière invalide !");
    }
  };

  const supprimerMatiere = async () => {
    if (!selectedMatiere || !selectedMatiere.idMatiere) {
      console.error(
        "Erreur : Aucune matière sélectionnée ou ID de matière invalide."
      );
      return;
    }

    try {
      await axiosInstance.delete(`/api/matieres/${selectedMatiere.idMatiere}`);

      // Update the UI state
      setMatieres((prevMatieres) =>
        prevMatieres.filter((m) => m.idMatiere !== selectedMatiere.idMatiere)
      );
      setShowModalDelete(false);
      setSelectedMatiere(null);
      setSuccessMessage("Matière supprimée avec succès");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Erreur lors de la suppression :", error);
      setError(
        error.response?.data?.message ||
          "Erreur lors de la suppression de la matière"
      );
    }
  };



  // Fonction pour ouvrir le modal de modification
  const ouvrirFormModif = (matiere) => {
    setMatiereModif(matiere);
    setNouveauNom(matiere.nomMatiere);
    setNouvelleDescription(matiere.description);
    setNouvelleDuree(matiere.duree);

    // Handle different ways niveaux might be stored
    let niveauxIds = [];
    if (matiere.niveaux && matiere.niveaux.length > 0) {
      niveauxIds = matiere.niveaux.map(niveau => niveau.id);
    } else if (matiere.niveauIds && matiere.niveauIds.length > 0) {
      niveauxIds = matiere.niveauIds.map(niv => typeof niv === 'object' ? niv.id : niv);
    } else if (matiere.matiereNiveaux && matiere.matiereNiveaux.length > 0) {
      niveauxIds = matiere.matiereNiveaux
        .filter(mn => mn.niveau)
        .map(mn => mn.niveau.id);
    }
    setNouveauNiveau(niveauxIds);

    // Handle different ways abonnements might be stored
    let abonnementsIds = [];
    if (matiere.abonnements && matiere.abonnements.length > 0) {
      abonnementsIds = matiere.abonnements.map(abonnement => abonnement.id);
    } else if (matiere.abonnementIds && matiere.abonnementIds.length > 0) {
      abonnementsIds = matiere.abonnementIds.map(ab => typeof ab === 'object' ? ab.id : ab);
    }
    setNouveauAbonnement(abonnementsIds);

    setNouvelleImage(null);
    setErrors({});

    console.log("Matière à modifier:", matiere);
    console.log("Niveaux IDs:", niveauxIds);
    console.log("Abonnements IDs:", abonnementsIds);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Vérifier le type de fichier
      if (!file.type.match("image.*")) {
        setErrors({ general: "Veuillez sélectionner un fichier image valide" });
        return;
      }

      // Vérifier la taille du fichier (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setErrors({
          general: "La taille de l'image ne doit pas dépasser 10MB",
        });
        return;
      }

      setNouvelleImage(file);
      setErrors({});
    }
  };

  const modifierMatiere = async () => {
    try {
      console.log("Starting modification with values:", {
        nouveauNom,
        nouvelleDescription,
        nouvelleDuree,
        hasImage: !!nouvelleImage
      });

      // Try a simpler approach - only update basic fields first
      // This avoids the Hibernate error with relationships
      const formData = new FormData();
      formData.append("nomMatiere", nouveauNom);
      formData.append("description", nouvelleDescription);
      formData.append("duree", nouvelleDuree);

      // Only add image if it exists
      if (nouvelleImage) {
        formData.append("image", nouvelleImage);
      }

      console.log("Sending simplified FormData request");

      // Log the FormData contents for debugging
      for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
      }

      // Use direct axios call without setting Content-Type (browser will set it)
      // Try the regular endpoint but with only basic fields
      const response = await axios({
        method: 'put',
        url: `http://localhost:8084/api/matieres/${matiereModif.idMatiere}`,
        data: formData,
        headers: {
          'Authorization': `Bearer ${keycloak.token}`
        }
      });

      console.log("Modification successful, response:", response.data);

      // Update the local state
      setMatieres((prevMatieres) =>
        prevMatieres.map((m) =>
          m.idMatiere === matiereModif.idMatiere
            ? {
                ...m,
                nomMatiere: nouveauNom,
                description: nouvelleDescription,
                duree: nouvelleDuree,
                niveaux: niveaux.filter((niveau) =>
                  nouveauNiveau.includes(niveau.id)
                ),
                abonnements: abonnements.filter((abonnement) =>
                  nouveauAbonnement.includes(abonnement.id)
                ),
                ...(response.data?.image && { image: response.data.image }),
              }
            : m
        )
      );

      // Refresh data to ensure we have the latest from the server
      try {
        await fetchData();
      } catch (refreshError) {
        console.warn("Error refreshing data after modification:", refreshError);
      }

      setMatiereModif(null);
      setSuccessMessage("Matière modifiée avec succès !");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Erreur lors de la modification :", error);

      // More detailed error logging
      if (error.response) {
        console.error("Response error status:", error.response.status);
        console.error("Response error data:", error.response.data);
        console.error("Response error headers:", error.response.headers);

        if (error.response.status === 415) {
          setError("Erreur de format: Le serveur n'accepte pas le format des données envoyées.");
        } else {
          setError(error.response.data?.message || "Une erreur est survenue lors de la modification de la matière");
        }
      } else if (error.request) {
        console.error("Request error - no response received:", error.request);
        setError("Erreur de connexion: Aucune réponse reçue du serveur.");
      } else {
        console.error("Error message:", error.message);
        setError("Une erreur est survenue lors de la modification de la matière");
      }
    }
  };


  // Fetch recordings for a specific matiere with optional niveau filter
  const fetchRecordings = async (matiereId, niveauId = null) => {
    try {
      console.log("Fetching recordings for matiere ID:", matiereId, "niveau ID:", niveauId);

      // Build the URL with or without the niveau filter
      let url = `/api/recordings/matiere/${matiereId}`;
      if (niveauId) {
        url = `/api/recordings/matiere/${matiereId}/niveau/${niveauId}`;
      }

      const response = await axiosInstance.get(url, {
        headers: { Authorization: `Bearer ${keycloak.token}` }
      });

      console.log("Recordings response:", response.data);

      // Update the recordings state for this matiere
      setLiveSessionRecordings(prev => ({
        ...prev,
        [matiereId]: response.data
      }));

      return response.data;
    } catch (error) {
      console.error("Error fetching recordings for matiere:", matiereId, "niveau:", niveauId, error);
      // Don't update state with empty array on error, keep previous state
      return liveSessionRecordings[matiereId] || [];
    }
  };

  // Show recordings modal for a specific matiere
  const handleShowRecordingsModal = async (matiere) => {
    setSelectedMatiereRecordings(matiere);
    setRecordingFile(null);
    setRecordingUploadSuccess("");
    setRecordingUploadError("");
    setFilterNiveau(""); // Reset the filter when opening the modal

    // Force refresh the teacher/admin role status
    const hasTeacherRole = keycloak.hasResourceRole("ENSEIGNANT") || keycloak.hasRealmRole("ENSEIGNANT");
    const hasAdminRoleCheck = keycloak.hasResourceRole("ADMIN") || keycloak.hasRealmRole("ADMIN");
    console.log("Current user roles:", keycloak.resourceAccess);
    console.log("Current realm roles:", keycloak.realmAccess);
    console.log("Is user a teacher:", hasTeacherRole, "Is user an admin:", hasAdminRoleCheck);

    // Update state variables with latest values
    setIsEnseignant(hasTeacherRole);
    setHasAdminRole(hasAdminRoleCheck);

    setShowRecordingsModal(true);

    // Fetch the latest recordings for this matiere
    await fetchRecordings(matiere.idMatiere);
  };

  // Handle recording file selection
  const handleRecordingFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Verify file is a video type
      if (!file.type.includes("video/")) {
        setRecordingUploadError("Veuillez sélectionner un fichier vidéo valide");
        return;
      }

      // Verify file size (max 1GB)
      if (file.size > 1024 * 1024 * 1024) {
        setRecordingUploadError("La taille de la vidéo ne doit pas dépasser 1GB");
        return;
      }

      setRecordingFile(file);
      setRecordingUploadError("");
    }
  };

  // Upload recording using the new API
  const handleUploadRecording = async () => {
    if (!recordingFile) {
      setRecordingUploadError("Veuillez sélectionner un fichier vidéo");
      return;
    }

    // Validate the selected niveau
    if (!selectedNiveau) {
      setRecordingUploadError("Veuillez sélectionner un niveau");
      return;
    }

    setUploadingRecording(true);
    setRecordingUploadSuccess("");
    setRecordingUploadError("");

    try {
      // Log debugging information
      console.log("Uploading recording for matiere:", selectedMatiereRecordings?.idMatiere);
      console.log("Selected niveau:", selectedNiveau);
      console.log("User roles:", keycloak.resourceAccess);

      const formData = new FormData();
      formData.append("file", recordingFile);
      formData.append("matiereId", selectedMatiereRecordings?.idMatiere);
      formData.append("niveauId", Number(selectedNiveau)); // Convert to number to ensure proper type
      formData.append("title", `Enregistrement ${new Date().toLocaleDateString()}`);
      formData.append("description", `Enregistrement du cours pour ${selectedMatiereRecordings?.nomMatiere}`);

      // Upload using the new recordings API
      await axiosInstance.post(
        `/api/recordings/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${keycloak.token}`
          }
        }
      );

      // Update the recordings list
      await fetchRecordings(selectedMatiereRecordings.idMatiere);

      setRecordingUploadSuccess("Enregistrement ajouté avec succès !");
      setRecordingFile(null);
      setSelectedNiveau(null);

      // Clear success message after 3 seconds
      setTimeout(() => setRecordingUploadSuccess(""), 3000);
    } catch (error) {
      console.error("Error uploading recording:", error);

      // Provide more specific error messages based on the error type
      if (error.message?.includes("Network Error") ||
          error.response?.data?.includes("api.vimeo.com") ||
          error.response?.status === 500) {
        setRecordingUploadError(
          "Problème de connexion au service de vidéo. Veuillez vérifier votre connexion internet ou réessayer plus tard. Si le problème persiste, contactez l'administrateur du système."
        );
      } else {
        setRecordingUploadError(
          error.response?.data || "Erreur lors de l'upload de l'enregistrement"
        );
      }
    } finally {
      setUploadingRecording(false);
    }
  };

  // Handle filter niveau change
  const handleFilterNiveauChange = async (e) => {
    const niveauId = e.target.value;
    setFilterNiveau(niveauId);

    if (selectedMatiereRecordings) {
      // If a niveau is selected, fetch filtered recordings
      // Otherwise, fetch all recordings for the matiere
      await fetchRecordings(
        selectedMatiereRecordings.idMatiere,
        niveauId ? Number(niveauId) : null
      );
    }
  };

  // Handle click for navigating to chapitres, showing modal for niveau selection if necessary
  const handleChapitreClick = (matiere) => {
    // If matiere has multiple niveaux, open a modal for niveau selection
    if (
      (matiere.niveaux && matiere.niveaux.length > 1) ||
      (matiere.matiereNiveaux && matiere.matiereNiveaux.length > 1)
    ) {
      setSelectedMatiere(matiere);
      setShowNiveauSelectModal(true);
    } else {
      // If it has only one niveau, navigate directly
      let niveauId = null;

      if (matiere.niveaux && matiere.niveaux.length === 1) {
        niveauId = matiere.niveaux[0].id;
      } else if (matiere.matiereNiveaux && matiere.matiereNiveaux.length === 1 && matiere.matiereNiveaux[0].niveau) {
        niveauId = matiere.matiereNiveaux[0].niveau.id;
      }

      if (niveauId) {
        navigate(`/chapitres/matiere/${matiere.idMatiere}/${niveauId}`);
      } else {
        // If no niveau exists, just navigate to the matiere
        navigate(`/chapitres/matiere/${matiere.idMatiere}`);
      }
    }
  };

  // Handle niveau selection from modal
  const handleNiveauSelect = (niveauId) => {
    if (selectedMatiere) {
      navigate(`/chapitres/matiere/${selectedMatiere.idMatiere}/${niveauId}`);
      setShowNiveauSelectModal(false);
      setSelectedMatiere(null);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between flex-wrap mb-4">
        {/* Titre */}
        <div className="col-12 col-sm-auto mb-2 mb-sm-0">
          <h4 style={{
            color: "var(--primary-blue)",
            fontWeight: "600",
            position: "relative",
            paddingBottom: "10px"
          }}>
            Toutes les Matières
            <span style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              width: "50px",
              height: "3px",
              backgroundColor: "var(--primary-yellow)"
            }}></span>
          </h4>
        </div>

        {/* Champ de recherche */}
        <div className="col-12 col-sm-6 col-md-4 mb-2 mb-sm-0">
          <div className="input-group">
            <div className="input-group-prepend">
              <span className="input-group-text" style={{ backgroundColor: "var(--primary-blue)", border: "none" }}>
                <i className="la la-search text-white"></i>
              </span>
            </div>
            <Form.Control
              type="text"
              placeholder="Rechercher une matière..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                borderLeft: "none",
                boxShadow: "none",
                borderColor: "#ced4da"
              }}
            />
          </div>
        </div>

        {/* Bouton Ajouter */}
        <div className="col-12 col-sm-auto mt-2 mt-sm-0">
          <button
            className="btn w-100"
            style={{
              backgroundColor: "var(--primary-yellow)",
              borderColor: "var(--primary-yellow)",
              color: "var(--primary-dark)",
              fontWeight: "600",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
              transition: "all 0.3s ease"
            }}
            onClick={() => navigate("/matieres/ajouter")}
          >
            <i className="la la-plus-circle mr-2"></i> Ajouter une Matière
          </button>
        </div>
      </div>

      {error && (
        <div className="alert alert-danger alert-dismissible fade show">
          <i className="fa fa-exclamation-triangle mr-2"></i> {error}
          <button type="button" className="close" onClick={() => setError(null)}>
            <span>&times;</span>
          </button>
        </div>
      )}

      {successMessage && (
        <div className="alert alert-success alert-dismissible fade show">
          <i className="fa fa-check-circle mr-2"></i> {successMessage}
          <button type="button" className="close" onClick={() => setSuccessMessage("")}>
            <span>&times;</span>
          </button>
        </div>
      )}

      {loading ? (
        <div className="loading-container">
          <div className="spinner-border loading-spinner text-primary" role="status">
            <span className="sr-only">Chargement...</span>
          </div>
          <p className="mt-3">Chargement des matières...</p>
        </div>
      ) : matieresFiltres.length === 0 ? (
        <div className="empty-state">
          <i className="fa fa-search mb-3" style={{ fontSize: "3rem", color: "#adb5bd" }}></i>
          <h4>Aucune matière trouvée</h4>
          <p className="text-muted">Essayez d'ajuster votre recherche ou ajoutez une nouvelle matière.</p>
          {hasAdminRole && (
            <button
              className="btn btn-primary btn-action mt-2"
              onClick={() => navigate("/matieres/ajouter")}
            >
              <i className="fa fa-plus-circle mr-1"></i> Ajouter une matière
            </button>
          )}
        </div>
      ) : (
        <div className="row">
          {matieresFiltres.map((matiere) => (
            <div key={matiere.idMatiere} className="col-12 col-sm-6 col-lg-4 col-xl-3 mb-4">
              <div className="matiere-card shadow-sm" style={{
                borderRadius: "8px",
                overflow: "hidden",
                border: "none",
                backgroundColor: "var(--card-bg)",
                transition: "transform 0.3s ease, box-shadow 0.3s ease",
                height: "100%",
                display: "flex",
                flexDirection: "column"
              }}>
                {matiere.image ? (
                  <div style={{ position: "relative", overflow: "hidden", height: "180px" }}>
                    <img
                      className="card-img-top"
                      src={
                        imageUrls[matiere.image.idImage] ||
                        "placeholder-image.jpg"
                      }
                      alt={matiere.nomMatiere}
                      onLoad={() =>
                        !imageUrls[matiere.image.idImage] &&
                        loadImage(matiere.image.idImage)
                      }
                      onError={(e) => {
                        // Retry loading once on error
                        if (!e.target.retryAttempted) {
                          e.target.retryAttempted = true;
                          loadImage(matiere.image.idImage);
                        }
                      }}
                      style={{
                        objectFit: "cover",
                        height: "100%",
                        width: "100%",
                        transition: "transform 0.5s ease"
                      }}
                    />
                    <div style={{
                      position: "absolute",
                      top: "10px",
                      right: "10px",
                      backgroundColor: "var(--primary-yellow)",
                      color: "var(--primary-dark)",
                      padding: "5px 10px",
                      borderRadius: "20px",
                      fontSize: "0.8rem",
                      fontWeight: "600"
                    }}>
                      {matiere.duree}H
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-5" style={{
                    backgroundColor: "var(--primary-light-green)",
                    height: "180px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center"
                  }}>
                    <i className="la la-image" style={{ fontSize: "3rem", color: "var(--primary-blue)" }}></i>
                    <p style={{ color: "var(--primary-dark)", marginTop: "10px" }}>Pas d'image</p>
                  </div>
                )}

                <div className="card-body d-flex flex-column" style={{ padding: "20px" }}>
                  <h4 className="card-title" style={{
                    color: "var(--primary-dark)",
                    fontSize: "1.25rem",
                    fontWeight: "600",
                    marginBottom: "15px",
                    borderBottom: "2px solid var(--primary-yellow)",
                    paddingBottom: "8px"
                  }}>{matiere.nomMatiere}</h4>

                  <div className="description-container" style={{
                    position: "relative",
                    maxHeight: "80px",
                    overflow: "hidden",
                    marginBottom: "15px"
                  }}>
                    <p style={{ color: "var(--text-secondary)", fontSize: "0.9rem" }}>{matiere.description}</p>
                    <div className="description-fade" style={{
                      position: "absolute",
                      bottom: "0",
                      left: "0",
                      width: "100%",
                      height: "30px",
                      background: "linear-gradient(to bottom, rgba(255,255,255,0), var(--card-bg))"
                    }}></div>
                  </div>

                  <div className="mb-3">
                    <div style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "8px",
                      color: "var(--primary-blue)",
                      fontWeight: "600"
                    }}>
                      <i className="la la-tag mr-2"></i>
                      <span>Abonnements</span>
                    </div>
                    <div className="d-flex flex-wrap">
                      {matiere.abonnements && matiere.abonnements.length > 0 ? (
                        matiere.abonnements.map((abonnement) => (
                          <span
                            key={abonnement.id}
                            style={{
                              backgroundColor: "var(--primary-blue)",
                              color: "white",
                              padding: "3px 8px",
                              borderRadius: "15px",
                              fontSize: "0.75rem",
                              marginRight: "5px",
                              marginBottom: "5px",
                              display: "inline-block"
                            }}
                          >
                            {abonnement.nom}
                          </span>
                        ))
                      ) : matiere.abonnementIds && matiere.abonnementIds.length > 0 ? (
                        matiere.abonnementIds.map((abId) => {
                          const abonnementId = typeof abId === 'object' ? abId.id : abId;
                          const abonnement = abonnements.find(a => a.id === abonnementId);
                          return (
                            <span
                              key={abonnementId}
                              style={{
                                backgroundColor: "var(--primary-blue)",
                                color: "white",
                                padding: "3px 8px",
                                borderRadius: "15px",
                                fontSize: "0.75rem",
                                marginRight: "5px",
                                marginBottom: "5px",
                                display: "inline-block"
                              }}
                            >
                              {abonnement?.nom || (typeof abId === 'object' ? abId.nom : 'Abonnement inconnu')}
                            </span>
                          );
                        })
                      ) : (
                        <span style={{
                          backgroundColor: "#f0f0f0",
                          color: "#666",
                          padding: "3px 8px",
                          borderRadius: "15px",
                          fontSize: "0.75rem",
                          marginBottom: "5px",
                          display: "inline-block"
                        }}>Aucun abonnement</span>
                      )}
                    </div>
                  </div>

                  <div className="mb-4">
                    <div style={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "8px",
                      color: "var(--primary-green)",
                      fontWeight: "600"
                    }}>
                      <i className="la la-layer-group mr-2"></i>
                      <span>Niveaux</span>
                    </div>
                    <div className="d-flex flex-wrap">
                      {matiere.niveaux && matiere.niveaux.length > 0 ? (
                        matiere.niveaux.map((niveau, index) => (
                          <span
                            key={niveau.id || index}
                            style={{
                              backgroundColor: "var(--primary-green)",
                              color: "white",
                              padding: "3px 8px",
                              borderRadius: "15px",
                              fontSize: "0.75rem",
                              marginRight: "5px",
                              marginBottom: "5px",
                              display: "inline-block"
                            }}
                          >
                            {niveau.nom}
                          </span>
                        ))
                      ) : matiere.niveauIds && matiere.niveauIds.length > 0 ? (
                        matiere.niveauIds.map((nivId) => {
                          const niveauId = typeof nivId === 'object' ? nivId.id : nivId;
                          const niveau = niveaux.find(n => n.id === niveauId);
                          return (
                            <span
                              key={niveauId}
                              style={{
                                backgroundColor: "var(--primary-green)",
                                color: "white",
                                padding: "3px 8px",
                                borderRadius: "15px",
                                fontSize: "0.75rem",
                                marginRight: "5px",
                                marginBottom: "5px",
                                display: "inline-block"
                              }}
                            >
                              {niveau?.nom || (typeof nivId === 'object' ? nivId.nom : 'Niveau inconnu')}
                            </span>
                          );
                        })
                      ) : matiere.matiereNiveaux && matiere.matiereNiveaux.length > 0 ? (
                        matiere.matiereNiveaux.map((mn, index) =>
                          mn.niveau ? (
                            <span
                              key={index}
                              style={{
                                backgroundColor: "var(--primary-green)",
                                color: "white",
                                padding: "3px 8px",
                                borderRadius: "15px",
                                fontSize: "0.75rem",
                                marginRight: "5px",
                                marginBottom: "5px",
                                display: "inline-block"
                              }}
                            >
                              {mn.niveau.nom}
                            </span>
                          ) : (
                            <span key={index} style={{
                              backgroundColor: "#f0f0f0",
                              color: "#666",
                              padding: "3px 8px",
                              borderRadius: "15px",
                              fontSize: "0.75rem",
                              marginBottom: "5px",
                              display: "inline-block"
                            }}>Niveau non disponible</span>
                          )
                        )
                      ) : (
                        <span style={{
                          backgroundColor: "#f0f0f0",
                          color: "#666",
                          padding: "3px 8px",
                          borderRadius: "15px",
                          fontSize: "0.75rem",
                          marginBottom: "5px",
                          display: "inline-block"
                        }}>Aucun niveau</span>
                      )}
                    </div>
                  </div>

                  <div className="mt-auto btn-group-vertical w-100">
                    <button
                      onClick={() => handleChapitreClick(matiere)}
                      style={{
                        backgroundColor: "var(--primary-blue)",
                        borderColor: "var(--primary-blue)",
                        color: "white",
                        borderRadius: "6px",
                        padding: "8px 15px",
                        marginBottom: "8px",
                        fontWeight: "500",
                        transition: "all 0.3s ease",
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                      }}
                      className="btn w-100"
                    >
                      <i className="la la-list mr-2"></i> Liste des chapitres
                    </button>

                    <button
                      onClick={() => handleShowRecordingsModal(matiere)}
                      style={{
                        backgroundColor: "var(--primary-navy)",
                        borderColor: "var(--primary-navy)",
                        color: "white",
                        borderRadius: "6px",
                        padding: "8px 15px",
                        marginBottom: "8px",
                        fontWeight: "500",
                        transition: "all 0.3s ease",
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                      }}
                      className="btn w-100"
                    >
                      <i className="la la-video-camera mr-2"></i> Enregistrements Live
                    </button>

                    {(isEnseignant || hasAdminRole) && (
                      <div className="d-flex mt-2">
                        <button
                          onClick={() => ouvrirFormModif(matiere)}
                          style={{
                            backgroundColor: "var(--primary-yellow)",
                            borderColor: "var(--primary-yellow)",
                            color: "var(--primary-dark)",
                            borderRadius: "6px",
                            padding: "8px 15px",
                            fontWeight: "500",
                            transition: "all 0.3s ease",
                            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                            flex: "1",
                            marginRight: "5px"
                          }}
                          className="btn"
                        >
                          <i className="la la-edit mr-1"></i> Modifier
                        </button>
                        <Button
                          style={{
                            backgroundColor: "#dc3545",
                            borderColor: "#dc3545",
                            color: "white",
                            borderRadius: "6px",
                            padding: "8px 15px",
                            fontWeight: "500",
                            transition: "all 0.3s ease",
                            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                            flex: "1",
                            marginLeft: "5px"
                          }}
                          onClick={() => handleShowDeleteModal(matiere)}
                        >
                          <i className="la la-trash mr-1"></i> Supprimer
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination : boutons pour naviguer entre les pages */}
      <div className="d-flex justify-content-center mt-4 mb-3 flex-wrap">
        <div className="pagination-container" style={{
          display: "flex",
          alignItems: "center",
          backgroundColor: "white",
          borderRadius: "30px",
          padding: "5px",
          boxShadow: "0 4px 10px rgba(0, 0, 0, 0.1)"
        }}>
          <button
            style={{
              backgroundColor: currentPage === 1 ? "#f0f0f0" : "var(--primary-blue)",
              color: currentPage === 1 ? "#999" : "white",
              border: "none",
              borderRadius: "25px",
              padding: "8px 20px",
              margin: "0 5px",
              fontWeight: "500",
              cursor: currentPage === 1 ? "not-allowed" : "pointer",
              transition: "all 0.3s ease",
              display: "flex",
              alignItems: "center"
            }}
            onClick={goToPrevPage}
            disabled={currentPage === 1}
          >
            <i className="la la-angle-left mr-1" style={{ fontSize: "1.2rem" }}></i> Précédent
          </button>

          <div style={{
            backgroundColor: "var(--primary-yellow)",
            color: "var(--primary-dark)",
            borderRadius: "20px",
            padding: "6px 15px",
            margin: "0 10px",
            fontWeight: "600",
            fontSize: "0.9rem"
          }}>
            Page {currentPage} sur {totalPages}
          </div>

          <button
            style={{
              backgroundColor: currentPage === totalPages ? "#f0f0f0" : "var(--primary-blue)",
              color: currentPage === totalPages ? "#999" : "white",
              border: "none",
              borderRadius: "25px",
              padding: "8px 20px",
              margin: "0 5px",
              fontWeight: "500",
              cursor: currentPage === totalPages ? "not-allowed" : "pointer",
              transition: "all 0.3s ease",
              display: "flex",
              alignItems: "center"
            }}
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
          >
            Suivant <i className="la la-angle-right ml-1" style={{ fontSize: "1.2rem" }}></i>
          </button>
        </div>
      </div>

      {matiereModif && (
        <div
          className="modal fade show d-block"
          style={{
            background: "rgba(0,0,0,0.7)",
            backdropFilter: "blur(4px)"
          }}
        >
          <div className="modal-dialog modal-dialog-centered modal-lg">
            <div className="modal-content" style={{
              borderRadius: "12px",
              border: "none",
              boxShadow: "0 10px 30px rgba(0,0,0,0.2)",
              overflow: "hidden"
            }}>
              <div className="modal-header" style={{
                backgroundColor: "var(--primary-blue)",
                color: "white",
                borderBottom: "none",
                padding: "15px 20px"
              }}>
                <h5 className="modal-title" style={{ fontWeight: "600" }}>
                  <i className="la la-edit mr-2"></i>
                  Modifier la matière
                </h5>
                <button
                  className="btn-close"
                  onClick={() => setMatiereModif(null)}
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    color: "white",
                    fontSize: "1.5rem",
                    opacity: "0.8",
                    transition: "opacity 0.3s ease"
                  }}
                >
                  <i className="la la-times"></i>
                </button>
              </div>

              <div className="modal-body" style={{ padding: "20px" }}>
                {successMessage && (
                  <div className="alert alert-success" style={{
                    backgroundColor: "rgba(40, 167, 69, 0.1)",
                    borderColor: "var(--primary-green)",
                    color: "var(--primary-green)",
                    borderRadius: "8px",
                    padding: "12px 15px"
                  }}>
                    <i className="la la-check-circle mr-2"></i>
                    {successMessage}
                  </div>
                )}
                {errors.general && (
                  <div className="alert alert-danger" style={{
                    backgroundColor: "rgba(220, 53, 69, 0.1)",
                    borderColor: "#dc3545",
                    color: "#dc3545",
                    borderRadius: "8px",
                    padding: "12px 15px"
                  }}>
                    <i className="la la-exclamation-circle mr-2"></i>
                    {errors.general}
                  </div>
                )}

                <div className="row">
                  <div className="col-12 mb-3">
                    <label htmlFor="nomMatiere" className="form-label">Nom de la matière</label>
                    <input
                      id="nomMatiere"
                      type="text"
                      className="form-control"
                      value={nouveauNom}
                      onChange={(e) => setNouveauNom(e.target.value)}
                      placeholder="Nom de la matière"
                    />
                  </div>

                  <div className="col-12 mb-3">
                    <label htmlFor="description" className="form-label">Description</label>
                    <textarea
                      id="description"
                      className="form-control"
                      value={nouvelleDescription}
                      onChange={(e) => setNouvelleDescription(e.target.value)}
                      placeholder="Description"
                      rows="3"
                    ></textarea>
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <label htmlFor="duree" className="form-label">Durée (heures)</label>
                    <input
                      id="duree"
                      type="number"
                      className="form-control"
                      value={nouvelleDuree}
                      onChange={(e) => setNouvelleDuree(e.target.value)}
                      placeholder="Durée"
                    />
                  </div>

                  <div className="col-12 col-md-6 mb-3">
                    <label htmlFor="image" className="form-label">Image</label>
                    <input
                      id="image"
                      type="file"
                      className="form-control"
                      onChange={handleFileChange}
                      accept="image/*"
                    />
                    <small className="text-muted">Format recommandé: JPG, PNG. Max 10MB</small>
                  </div>

                  <div className="col-12 mb-3">
                    <label htmlFor="niveau" className="form-label">Niveaux</label>
                    <Select
                      id="niveau"
                      isMulti
                      value={nouveauNiveau
                        .map((id) => {
                          const niveau = niveaux.find((a) => a.id === id);
                          return niveau
                            ? { value: niveau.id, label: niveau.nom }
                            : null;
                        })
                        .filter(Boolean)}
                      options={niveaux.map((niveau) => ({
                        value: niveau.id,
                        label: `${niveau.nom} `,
                      }))}
                      onChange={(selectedOptions) => {
                        setNouveauNiveau(
                          selectedOptions
                            ? selectedOptions.map((option) => option.value)
                            : []
                        );
                      }}
                      placeholder="Sélectionner plusieurs niveaux"
                      styles={{
                        control: (provided) => ({
                          ...provided,
                          width: "100%",
                          borderColor: "#ccc",
                          borderRadius: "4px",
                          padding: "5px",
                        }),
                        menu: (provided) => ({
                          ...provided,
                          borderRadius: "4px",
                          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        }),
                      }}
                    />
                  </div>

                  <div className="col-12 mb-3">
                    <label htmlFor="abonnement" className="form-label">Abonnements</label>
                    <Select
                      id="abonnement"
                      isMulti
                      value={nouveauAbonnement
                        .map((id) => {
                          const abonnement = abonnements.find((a) => a.id === id);
                          return abonnement
                            ? { value: abonnement.id, label: abonnement.nom }
                            : null;
                        })
                        .filter(Boolean)}
                      options={abonnements.map((abonnement) => ({
                        value: abonnement.id,
                        label: `${abonnement.nom} - ${abonnement.prix} DT`,
                      }))}
                      onChange={(selectedOptions) => {
                        setNouveauAbonnement(
                          selectedOptions
                            ? selectedOptions.map((option) => option.value)
                            : []
                        );
                      }}
                      placeholder="Sélectionner plusieurs abonnements"
                      styles={{
                        control: (provided) => ({
                          ...provided,
                          width: "100%",
                          borderColor: "#ccc",
                          borderRadius: "4px",
                          padding: "5px",
                        }),
                        menu: (provided) => ({
                          ...provided,
                          borderRadius: "4px",
                          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                        }),
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="modal-footer" style={{
                borderTop: "1px solid #eee",
                padding: "15px 20px",
                display: "flex",
                justifyContent: "space-between"
              }}>
                <button
                  style={{
                    backgroundColor: "transparent",
                    borderColor: "#dc3545",
                    color: "#dc3545",
                    borderRadius: "6px",
                    padding: "8px 20px",
                    fontWeight: "500",
                    transition: "all 0.3s ease"
                  }}
                  onClick={() => setMatiereModif(null)}
                >
                  <i className="la la-times mr-2"></i> Annuler
                </button>
                <button
                  style={{
                    backgroundColor: "var(--primary-blue)",
                    borderColor: "var(--primary-blue)",
                    color: "white",
                    borderRadius: "6px",
                    padding: "8px 20px",
                    fontWeight: "500",
                    transition: "all 0.3s ease",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
                  }}
                  onClick={modifierMatiere}
                >
                  <i className="la la-save mr-2"></i> Enregistrer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmation de suppression */}
      <Modal
        show={showModalDelete}
        onHide={() => setShowModalDelete(false)}
        centered
        size="sm"
        contentClassName="border-0 shadow"
        backdropClassName="bg-dark bg-opacity-75"
      >
        <Modal.Header
          closeButton
          style={{
            backgroundColor: "var(--primary-navy)",
            color: "white",
            border: "none",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px"
          }}
        >
          <Modal.Title className="fs-5">
            <i className="la la-exclamation-triangle mr-2"></i>
            Confirmer la suppression
          </Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ padding: "20px" }}>
          <div className="text-center mb-3">
            <div style={{
              width: "60px",
              height: "60px",
              borderRadius: "50%",
              backgroundColor: "rgba(220, 53, 69, 0.1)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              margin: "0 auto 15px auto"
            }}>
              <i className="la la-trash" style={{ fontSize: "2rem", color: "#dc3545" }}></i>
            </div>
          </div>
          <p className="mb-0 text-center" style={{ fontSize: "1rem" }}>
            Êtes-vous sûr de vouloir supprimer la matière{" "}
            <strong style={{ color: "var(--primary-blue)" }}>{selectedMatiere?.nomMatiere}</strong> ?
          </p>
          <p className="text-muted text-center mt-2 small">Cette action est irréversible.</p>
        </Modal.Body>
        <Modal.Footer className="justify-content-center border-0" style={{ padding: "0 20px 20px 20px" }}>
          <Button
            style={{
              backgroundColor: "transparent",
              borderColor: "#6c757d",
              color: "#6c757d",
              borderRadius: "6px",
              padding: "8px 20px",
              fontWeight: "500",
              transition: "all 0.3s ease",
              marginRight: "10px"
            }}
            onClick={() => setShowModalDelete(false)}
            className="w-100 w-sm-auto"
          >
            <i className="la la-times mr-2"></i> Annuler
          </Button>
          <Button
            style={{
              backgroundColor: "#dc3545",
              borderColor: "#dc3545",
              color: "white",
              borderRadius: "6px",
              padding: "8px 20px",
              fontWeight: "500",
              transition: "all 0.3s ease",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
            }}
            onClick={supprimerMatiere}
            className="w-100 w-sm-auto mt-2 mt-sm-0"
          >
            <i className="la la-trash mr-2"></i> Supprimer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal for Live Session Recordings */}
      <Modal
        show={showRecordingsModal}
        onHide={() => setShowRecordingsModal(false)}
        size="lg"
        centered
        className="recordings-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title className="text-truncate">
            Enregistrements des séances live - {selectedMatiereRecordings?.nomMatiere}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-2">
            {/* Debug info - remove in production */}
            <div className="small text-muted mb-2 d-none">
              Status: {isEnseignant ? "Enseignant/Admin détecté" : "Non détecté comme Enseignant/Admin"}
              <br />
              Admin role: {keycloak.hasRealmRole("ADMIN") ? "Yes (Realm)" : "No (Realm)"} / {keycloak.hasResourceRole("ADMIN") ? "Yes (Resource)" : "No (Resource)"}
              <br />
              Teacher role: {keycloak.hasRealmRole("ENSEIGNANT") ? "Yes (Realm)" : "No (Realm)"} / {keycloak.hasResourceRole("ENSEIGNANT") ? "Yes (Resource)" : "No (Resource)"}
            </div>
          </div>

          {(isEnseignant || keycloak.hasRealmRole("ADMIN") || keycloak.hasRealmRole("ENSEIGNANT")) && (
            <div className="mb-4 p-3 border rounded">
              <h5 className="mb-3">Ajouter un nouvel enregistrement</h5>

              {recordingUploadSuccess && (
                <div className="alert alert-success">{recordingUploadSuccess}</div>
              )}

              {recordingUploadError && (
                <div className="alert alert-danger">{recordingUploadError}</div>
              )}

              <div className="row">
                <div className="col-12 col-md-6 mb-3">
                  <div className="form-group">
                    <label htmlFor="recordingFile" className="form-label">Fichier vidéo</label>
                    <div className="custom-file">
                      <input
                        type="file"
                        className="custom-file-input"
                        id="recordingFile"
                        accept="video/*"
                        onChange={handleRecordingFileChange}
                        disabled={uploadingRecording}
                      />
                      <label className="custom-file-label" htmlFor="recordingFile">
                        {recordingFile ? recordingFile.name : "Choisir un fichier vidéo"}
                      </label>
                    </div>
                    <small className="form-text text-muted">Format recommandé: MP4, MOV. Max 1GB</small>
                  </div>
                </div>

                <div className="col-12 col-md-6 mb-3">
                  {/* Add niveau dropdown */}
                  <div className="form-group">
                    <label htmlFor="recordingNiveau" className="form-label">Niveau</label>
                    <select
                      className="form-control"
                      id="recordingNiveau"
                      value={selectedNiveau || ""}
                      onChange={(e) => setSelectedNiveau(e.target.value)}
                      disabled={uploadingRecording}
                    >
                      <option value="">Sélectionner un niveau</option>
                      {niveaux.map((niveau) => (
                        <option key={niveau.id} value={niveau.id}>
                          {niveau.nom}
                        </option>
                      ))}
                    </select>
                    <small className="form-text text-muted">Associer l'enregistrement à un niveau</small>
                  </div>
                </div>
              </div>

              <div className="form-group mt-3">
                <button
                  className="btn btn-primary w-100"
                  onClick={handleUploadRecording}
                  disabled={uploadingRecording || !recordingFile}
                >
                  <i className="fa fa-upload mr-2"></i>
                  Uploader l'enregistrement
                </button>
              </div>

              {uploadingRecording && (
                <div className="text-center mt-3">
                  <div className="spinner-border text-primary" role="status">
                    <span className="sr-only">Chargement...</span>
                  </div>
                  <p className="mt-2 mb-0">Upload en cours... Cela peut prendre quelques minutes</p>
                </div>
              )}
            </div>
          )}

          <div className="d-flex justify-content-between align-items-center mb-3 flex-wrap">
            <h5 className="mb-2 mb-sm-0">Enregistrements disponibles</h5>
            <div className="form-group mb-0" style={{ minWidth: "200px" }}>
              <select
                className="form-control form-control-sm"
                value={filterNiveau}
                onChange={handleFilterNiveauChange}
              >
                <option value="">Tous les niveaux</option>
                {niveaux.map((niveau) => (
                  <option key={niveau.id} value={niveau.id}>
                    {niveau.nom}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {liveSessionRecordings[selectedMatiereRecordings?.idMatiere]?.length > 0 ? (
            <div className="table-responsive">
              <table className="table table-striped">
                <thead className="thead-light">
                  <tr>
                    <th>Titre</th>
                    <th>Niveau</th>
                    <th>Date</th>
                    <th>Vidéo</th>
                  </tr>
                </thead>
                <tbody>
                  {liveSessionRecordings[selectedMatiereRecordings?.idMatiere]
                    ?.map(recording => {
                      // Extract video ID from Vimeo URL
                      const vimeoMatch = recording.videoUrl?.match(/(?:\/videos\/|vimeo\.com\/|)(\d+)/);
                      const videoId = vimeoMatch ? vimeoMatch[1] : null;

                      return (
                        <tr key={recording.id}>
                          <td className="text-truncate" style={{maxWidth: "150px"}}>{recording.title}</td>
                          <td>{recording.niveau?.nom || 'Non spécifié'}</td>
                          <td>
                            {recording.recordingDate ?
                              new Date(recording.recordingDate).toLocaleDateString() :
                              'Non spécifiée'}
                          </td>
                          <td>
                            {videoId ? (
                              <button
                                className="btn btn-sm btn-info"
                                onClick={() => {
                                  // Open in modal or in new tab
                                  window.open(`https://player.vimeo.com/video/${videoId}`, '_blank');
                                }}
                              >
                                <i className="fa fa-play mr-1"></i> Voir
                              </button>
                            ) : (
                              <a
                                href={recording.videoUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn btn-sm btn-info"
                              >
                                <i className="fa fa-play mr-1"></i> Voir
                              </a>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="alert alert-info text-center">
              <i className="fa fa-info-circle mr-2"></i>
              Aucun enregistrement disponible pour cette matière.
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowRecordingsModal(false)}>
            <i className="fa fa-times mr-1"></i> Fermer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal for Niveau Selection */}
      <Modal
        show={showNiveauSelectModal}
        onHide={() => setShowNiveauSelectModal(false)}
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title>Sélectionner un Niveau</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Veuillez sélectionner un niveau pour continuer.</p>
          <Select
            options={niveaux.map((niveau) => ({
              value: niveau.id,
              label: niveau.nom,
            }))}
            onChange={(selectedOption) => handleNiveauSelect(selectedOption.value)}
            placeholder="Sélectionner un niveau"
          />
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowNiveauSelectModal(false)}>
            Annuler
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AfficherMatiere;
