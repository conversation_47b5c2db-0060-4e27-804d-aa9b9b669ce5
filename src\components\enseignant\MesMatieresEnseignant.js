import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";
import keycloak from "../../keycloak";

const MesMatieresEnseignant = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [niveaux, setNiveaux] = useState([]);
  const [abonnements, setAbonnements] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [matieres, setMatieres] = useState([]);
  const [imageUrls, setImageUrls] = useState({});
  const [userId, setUserId] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");

  const loadImage = async (imageId, accessToken) => {
    try {
      const response = await axiosInstance.get(`/api/image/load/${imageId}`, {
        responseType: "blob",
        headers: {
          Accept: "image/*",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const blobUrl = URL.createObjectURL(new Blob([response.data]));
      setImageUrls((prev) => ({ ...prev, [imageId]: blobUrl }));
    } catch (error) {
      console.error("Error loading image:", error);
      setImageUrls((prev) => ({
        ...prev,
        [imageId]: prev[imageId] || "placeholder-image.jpg",
      }));
    }
  };

  useEffect(() => {
    // Cleanup function to revoke blob URLs
    return () => {
      Object.values(imageUrls).forEach((url) => URL.revokeObjectURL(url));
    };
  }, [imageUrls]);

  useEffect(() => {
    if (keycloak.authenticated) {
      const fetchEnseignantId = async () => {
        try {
          const tokenParsed = keycloak.tokenParsed;
          const username = tokenParsed.preferred_username;

          const response = await axiosInstance.get("/api/enseignants");

          if (response.data) {
            const enseignant = response.data.find(
              (e) =>
                e.username === username ||
                e.email === keycloak.tokenParsed.email
            );

            if (enseignant) {
              setUserId(enseignant.id || enseignant.idEnseignant);
            } else {
              if (response.data.length > 0) {
                const firstEnseignant = response.data[0];
                setUserId(firstEnseignant.id || firstEnseignant.idEnseignant);
              } else {
                setError("Aucun enseignant trouvé dans le système");
              }
            }
          }
        } catch (error) {
          console.error("Error fetching enseignants:", error);
          setError("Impossible de récupérer la liste des enseignants");
        }
      };

      fetchEnseignantId();
    }
  }, []);

  const abonnementsParPage = 8;

  useEffect(() => {
    const fetchData = async () => {
      if (!userId) return;

      try {
        setLoading(true);
        setError(null);

        const matieresRes = await axiosInstance.get(
          `/api/enseignants/${userId}/matieres`
        );

        const [niveauxRes, abonnementsRes] = await Promise.all([
          axiosInstance.get("/api/niveaux/all"),
          axiosInstance.get("/api/abonnements/all"),
        ]);

        if (matieresRes.data) {
          const allMatieres = matieresRes.data.content || matieresRes.data;

          // Pour chaque matière, récupérer les détails complets
          const matieresWithDetails = await Promise.all(
            allMatieres.map(async (matiere) => {
              try {
                // Récupérer les détails complets de la matière
                const matiereDetailRes = await axiosInstance.get(`/api/matieres/${matiere.idMatiere}`);
                return matiereDetailRes.data;
              } catch (error) {
                console.error(`Erreur lors de la récupération des détails de la matière ${matiere.idMatiere}:`, error);
                return matiere;
              }
            })
          );

          console.log("Matières avec détails:", matieresWithDetails);
          setMatieres(matieresWithDetails);
          setTotalPages(
            Math.ceil(matieresWithDetails.length / abonnementsParPage) || 1
          );
        }

        if (niveauxRes.data) {
          setNiveaux(niveauxRes.data);
        }

        if (abonnementsRes.data) {
          setAbonnements(abonnementsRes.data);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        if (err.response?.status === 401) {
          setError("Session expirée. Veuillez vous reconnecter.");
        } else {
          setError(
            err.response?.data?.message ||
              "Erreur lors du chargement des données"
          );
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userId, currentPage]);

  const indexOfLastMatiere = currentPage * abonnementsParPage;
  const indexOfFirstMatiere = indexOfLastMatiere - abonnementsParPage;
  const currentMatieres = matieres.slice(
    indexOfFirstMatiere,
    indexOfLastMatiere
  );

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const matieresFiltres = matieres.filter((matiere) => {
    return (
      (matiere.nomMatiere &&
        typeof matiere.nomMatiere === "string" &&
        matiere.nomMatiere.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (matiere.niveau &&
        typeof matiere.niveau === "string" &&
        matiere.niveau.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (matiere.duree &&
        !isNaN(matiere.duree) &&
        matiere.duree.toString().includes(searchTerm))
    );
  });

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between">
        <div className="col-auto">
          <h4 style={{ color: "#37A7DF" }}>Mes Matières</h4>
        </div>
        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="Rechercher une matière..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}
      {successMessage && (
        <div className="alert alert-success">{successMessage}</div>
      )}
      {loading && <div className="text-center">Chargement...</div>}

      <div className="row">
        {matieresFiltres.length === 0 ? (
          <p>Aucune matière trouvée.</p>
        ) : (
          matieresFiltres.map((matiere) => {
            return (
              <div
                key={matiere.idMatiere}
                className="col-xl-3 col-lg-4 col-md-6 col-sm-6"
              >
                <div className="card">
                  {matiere.image ? (
                    <img
                      className="card-img-top"
                      src={
                        imageUrls[matiere.image.idImage] ||
                        "placeholder-image.jpg"
                      }
                      alt={matiere.nomMatiere}
                      style={{ height: "150px", objectFit: "cover" }}
                      onLoad={() =>
                        !imageUrls[matiere.image.idImage] &&
                        loadImage(matiere.image.idImage)
                      }
                      onError={(e) => {
                        if (!e.target.retryAttempted) {
                          e.target.retryAttempted = true;
                          loadImage(matiere.image.idImage);
                        }
                      }}
                    />
                  ) : (
                    <p className="text-muted text-center">Pas d'image</p>
                  )}
                  <div className="card-body">
                    <h4>{matiere.nomMatiere}</h4>
                    <p>{matiere.description}</p>
                    <p>
                      <strong>Durée :</strong> {matiere.duree}H
                    </p>
                    <p>
                      <strong>Abonnement :</strong>{" "}
                      {matiere.abonnements && matiere.abonnements.length > 0
                        ? matiere.abonnements.map(a => a.nom).join(", ")
                        : (matiere.abonnement?.nom || "Non défini")}
                    </p>
                    <p>
                      <strong>Niveau :</strong>{" "}
                      {matiere.matiereNiveaux && matiere.matiereNiveaux.length > 0
                        ? matiere.matiereNiveaux.map(mn => mn.niveau?.nom).filter(Boolean).join(", ")
                        : (matiere.niveau?.nom || "Non défini")}
                    </p>
                    <div>
                      <button
                        onClick={() =>
                          navigate(`/chapitres/matiere/${matiere.idMatiere}`)
                        }
                        className="btn btn-primary mt-2 w-100 btn-sm"
                        style={{
                          backgroundColor: "#37A7DF",
                          borderColor: "#37A7DF",
                        }}
                      >
                        Liste des chapitres
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      <div className="d-flex justify-content-center mt-3">
        <button
          className="btn btn-secondary mx-1"
          onClick={goToPrevPage}
          disabled={currentPage === 1}
        >
          Précédent
        </button>
        <span className="mx-2">
          Page {currentPage} sur {totalPages}
        </span>
        <button
          className="btn btn-secondary mx-1"
          onClick={goToNextPage}
          disabled={currentPage === totalPages}
        >
          Suivant
        </button>
      </div>
    </div>
  );
};

export default MesMatieresEnseignant;
