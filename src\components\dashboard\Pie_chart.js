/**
 * ========================================================================
 * COMPOSANT GRAPHIQUE EN SECTEURS (PIE CHART) - SYSTÈME ÉDUCATIF
 * ========================================================================
 *
 * DESCRIPTION :
 * Ce composant affiche un graphique en secteurs (pie chart) interactif et
 * élégant qui présente la répartition des différentes entités du système
 * éducatif ThinkTrend.
 *
 * FONCTIONNALITÉS PRINCIPALES :
 * ✅ Récupération automatique des statistiques via l'API REST
 * ✅ Graphique interactif avec animations fluides et transitions
 * ✅ Tooltips informatifs avec pourcentages calculés dynamiquement
 * ✅ Légende avec icônes thématiques et couleurs cohérentes
 * ✅ Cartes statistiques détaillées sous le graphique
 * ✅ Gestion complète des états (chargement, erreur, données vides)
 * ✅ Design responsive s'adaptant à tous les écrans
 * ✅ Palette de couleurs conforme au design system
 *
 * DONNÉES AFFICHÉES :
 * 🎓 Cours - Nombre total de cours disponibles (Couleur: #37A7DF - Bleu)
 * 💳 Abonnements - Types d'abonnements proposés (Couleur: #F2BC00 - Jaune)
 * 📊 Niveaux - Niveaux d'étude disponibles (Couleur: #248E39 - Vert)
 * 📚 Matières - Matières enseignées (Couleur: #000080 - Bleu foncé)
 *
 * STRUCTURE DU COMPOSANT :
 * 1. États React (data, loading, error, total)
 * 2. Hook useEffect pour la récupération des données
 * 3. Fonction CustomTooltip pour les infobulles
 * 4. Rendu conditionnel selon l'état (chargement/erreur/données)
 * 5. Graphique principal avec Recharts
 * 6. Cartes statistiques résumées
 *
 * UTILISATION :
 * import PieChart from './components/dashboard/Pie_chart';
 *
 * function Dashboard() {
 *   return (
 *     <div className="col-lg-6">
 *       <PieChart />
 *     </div>
 *   );
 * }
 *
 * PRÉREQUIS TECHNIQUES :
 * - Bibliothèque recharts installée (npm install recharts)
 * - Service axiosInstance configuré pour les appels API
 * - Endpoint API /api/statistics disponible et fonctionnel
 * - Icônes FontAwesome chargées dans l'application
 * - Bootstrap pour les classes CSS (spinner, alert, etc.)
 *
 * FORMAT DE DONNÉES API ATTENDU :
 * {
 *   "totalCours": 25,
 *   "totalAbonnements": 3,
 *   "totalNiveaux": 5,
 *   "totalMatiere": 12
 * }
 *
 * <AUTHOR> de développement ThinkTrend
 * @version 1.0
 * @since 2025-01-24
 * @lastModified 2025-01-24
 * ========================================================================
 */

import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import {
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
} from "recharts";

/**
 * PALETTE DE COULEURS POUR LES SECTEURS DU GRAPHIQUE
 *
 * Chaque couleur correspond à une entité spécifique du système :
 * - Index 0 (#37A7DF) : Cours (Bleu principal)
 * - Index 1 (#F2BC00) : Abonnements (Jaune/Orange)
 * - Index 2 (#248E39) : Niveaux (Vert)
 * - Index 3 (#000080) : Matières (Bleu foncé)
 *
 * Ces couleurs sont conformes au design system de l'application
 * et garantissent une bonne lisibilité et accessibilité.
 */
const COLORS = ["#37A7DF", "#F2BC00", "#248E39", "#000080"];

/**
 * CONFIGURATION DES COULEURS DU THÈME
 *
 * Définit les couleurs utilisées pour les éléments d'interface :
 * - BACKGROUND : Couleur de fond principal des conteneurs
 * - LIGHT_BG : Couleur de fond claire pour les zones d'accent
 * - TEXT_DARK : Couleur du texte principal (contraste élevé)
 * - NEUTRAL : Couleur pour les éléments secondaires et bordures
 * - PRIMARY : Couleur principale de la marque (utilisée pour les accents)
 */
const THEME_COLORS = {
  BACKGROUND: "#F6F4EE",    // Crème - Fond principal
  LIGHT_BG: "#EEF9F5",     // Vert très clair - Zones d'accent
  TEXT_DARK: "#1D1D1B",    // Noir/Gris foncé - Texte principal
  NEUTRAL: "#B7B7B7",      // Gris - Éléments secondaires
  PRIMARY: "#37A7DF"       // Bleu - Couleur de marque
};

/**
 * COMPOSANT PRINCIPAL PIECHART
 *
 * Ce composant gère l'affichage complet du graphique en secteurs,
 * incluant la récupération des données, la gestion des états,
 * et le rendu conditionnel selon la situation.
 *
 * @returns {JSX.Element} Le composant React complet avec graphique et statistiques
 */
const PieChart = () => {
  // ====================================================================
  // ÉTATS DU COMPOSANT
  // ====================================================================

  /**
   * Données formatées pour le graphique
   * Structure : [{ name: string, value: number, icon: string }]
   */
  const [data, setData] = useState([]);

  /**
   * État de chargement des données depuis l'API
   * true = en cours de chargement, false = chargement terminé
   */
  const [loading, setLoading] = useState(true);

  /**
   * Message d'erreur en cas d'échec de récupération des données
   * null = pas d'erreur, string = message d'erreur à afficher
   */
  const [error, setError] = useState(null);

  /**
   * Somme totale de toutes les entités pour calcul des pourcentages
   * Utilisé dans les tooltips et les cartes statistiques
   */
  const [total, setTotal] = useState(0);

  // ====================================================================
  // RÉCUPÉRATION DES DONNÉES
  // ====================================================================

  /**
   * Hook useEffect pour charger les données au montage du composant
   * Se déclenche une seule fois lors du premier rendu (dépendance vide [])
   */
  useEffect(() => {
    /**
     * FONCTION ASYNCHRONE DE RÉCUPÉRATION DES STATISTIQUES
     *
     * Cette fonction :
     * 1. Appelle l'API /api/statistics
     * 2. Transforme les données au format requis par le graphique
     * 3. Filtre les données nulles ou vides
     * 4. Calcule le total pour les pourcentages
     * 5. Met à jour les états du composant
     * 6. Gère les erreurs et les états de chargement
     */
    const fetchStatistics = async () => {
      try {
        // Initialisation de l'état de chargement
        setLoading(true);
        setError(null);

        // ============================================================
        // APPEL API POUR RÉCUPÉRER LES STATISTIQUES
        // ============================================================
        const response = await axiosInstance.get("/api/statistics");
        console.log("📊 Données statistiques reçues:", response.data);

        const stats = response.data;

        // ============================================================
        // TRANSFORMATION DES DONNÉES POUR LE GRAPHIQUE
        // ============================================================
        // Création du tableau de données avec structure standardisée
        const chartData = [
          {
            name: "Cours",                    // Nom affiché dans la légende
            value: stats.totalCours || 0,     // Valeur numérique (avec fallback à 0)
            icon: "🎓"                        // Icône thématique
          },
          {
            name: "Abonnements",
            value: stats.totalAbonnements || 0,
            icon: "💳"
          },
          {
            name: "Niveaux",
            value: stats.totalNiveaux || 0,
            icon: "📊"
          },
          {
            name: "Matières",
            value: stats.totalMatiere || 0,
            icon: "📚"
          }
        ];

        // ============================================================
        // FILTRAGE ET CALCULS
        // ============================================================
        // Filtrer les données avec valeur > 0 pour un graphique plus lisible
        const filteredData = chartData.filter(item => item.value > 0);

        // Calculer le total de toutes les entités pour les pourcentages
        const totalValue = chartData.reduce((sum, item) => sum + item.value, 0);

        // ============================================================
        // MISE À JOUR DES ÉTATS
        // ============================================================
        // Utiliser les données filtrées si disponibles, sinon toutes les données
        setData(filteredData.length > 0 ? filteredData : chartData);
        setTotal(totalValue);

        console.log("✅ Données formatées avec succès:", filteredData);

      } catch (err) {
        // ============================================================
        // GESTION DES ERREURS
        // ============================================================
        console.error("❌ Erreur lors du chargement des statistiques:", err);

        // Définir un message d'erreur utilisateur-friendly
        const errorMessage = err.response?.data?.message ||
                           err.message ||
                           "Erreur lors du chargement des statistiques";
        setError(errorMessage);

      } finally {
        // ============================================================
        // FINALISATION
        // ============================================================
        // Arrêter l'état de chargement dans tous les cas
        setLoading(false);
      }
    };

    // Exécuter la fonction de récupération des données
    fetchStatistics();
  }, []); // Dépendance vide = exécution uniquement au montage

  // ====================================================================
  // COMPOSANTS PERSONNALISÉS
  // ====================================================================

  /**
   * COMPOSANT TOOLTIP PERSONNALISÉ
   *
   * Affiche une infobulle élégante lors du survol des secteurs du graphique.
   * Contient :
   * - L'icône et le nom de l'entité
   * - La valeur numérique
   * - Le pourcentage par rapport au total
   *
   * @param {boolean} active - Indique si le tooltip est actif (survol)
   * @param {Array} payload - Données du secteur survolé
   * @returns {JSX.Element|null} Le tooltip ou null si inactif
   */
  const CustomTooltip = ({ active, payload }) => {
    // Vérifier si le tooltip doit être affiché
    if (active && payload && payload.length) {
      const data = payload[0].payload;

      return (
        <div style={{
          backgroundColor: THEME_COLORS.BACKGROUND,    // Fond crème
          border: `2px solid ${THEME_COLORS.PRIMARY}`,  // Bordure bleue
          borderRadius: "8px",                          // Coins arrondis
          padding: "10px",                              // Espacement interne
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",    // Ombre portée
          fontSize: "14px"                              // Taille de police
        }}>
          {/* Ligne 1 : Icône + Nom de l'entité */}
          <p style={{
            margin: "0",
            fontWeight: "600",
            color: THEME_COLORS.TEXT_DARK
          }}>
            {data.icon} {data.name}
          </p>

          {/* Ligne 2 : Valeur + Pourcentage */}
          <p style={{
            margin: "5px 0 0 0",
            fontSize: "16px",
            fontWeight: "bold",
            color: payload[0].color
          }}>
            {data.value} ({total > 0 ? ((data.value / total) * 100).toFixed(1) : 0}%)
          </p>
        </div>
      );
    }

    // Retourner null si le tooltip ne doit pas être affiché
    return null;
  };

  // ====================================================================
  // RENDU CONDITIONNEL SELON L'ÉTAT
  // ====================================================================

  /**
   * RENDU DE L'ÉTAT DE CHARGEMENT
   *
   * Affiché pendant la récupération des données depuis l'API.
   * Contient un spinner animé et un message informatif.
   *
   * @returns {JSX.Element} Interface de chargement
   */
  if (loading) {
    return (
      <div style={{
        padding: "20px",
        textAlign: "center",
        backgroundColor: THEME_COLORS.BACKGROUND,
        borderRadius: "15px",
        boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
      }}>
        <h5 style={{ color: THEME_COLORS.PRIMARY, marginBottom: "20px" }}>
          🥧 Répartition des Entités
        </h5>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "200px",
          color: THEME_COLORS.PRIMARY
        }}>
          <div className="spinner-border me-2" role="status" aria-hidden="true"></div>
          Chargement...
        </div>
      </div>
    );
  }

  /**
   * RENDU DE L'ÉTAT D'ERREUR
   *
   * Affiché en cas d'échec de récupération des données.
   * Contient le message d'erreur et un bouton pour réessayer.
   *
   * @returns {JSX.Element} Interface d'erreur avec possibilité de réessai
   */
  if (error) {
    return (
      <div style={{
        padding: "20px",
        backgroundColor: THEME_COLORS.BACKGROUND,
        borderRadius: "15px",
        boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
      }}>
        <h5 style={{ color: THEME_COLORS.PRIMARY, marginBottom: "20px" }}>
          🥧 Répartition des Entités
        </h5>
        <div className="alert alert-danger" role="alert">
          <i className="fa fa-exclamation-triangle me-2"></i>
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => window.location.reload()}
          style={{ backgroundColor: THEME_COLORS.PRIMARY, borderColor: THEME_COLORS.PRIMARY }}
        >
          <i className="fa fa-refresh me-2"></i>
          Réessayer
        </button>
      </div>
    );
  }

  /**
   * RENDU PRINCIPAL DU COMPOSANT
   *
   * Affiché lorsque les données sont chargées avec succès.
   * Contient :
   * 1. En-tête avec titre et informations générales
   * 2. Message informatif si aucune donnée
   * 3. Graphique en secteurs interactif
   * 4. Cartes statistiques détaillées
   * 5. État vide si aucune donnée disponible
   *
   * @returns {JSX.Element} Interface complète du graphique
   */
  return (
    <div style={{
      backgroundColor: THEME_COLORS.BACKGROUND,
      borderRadius: "15px",
      padding: "25px",
      boxShadow: "0 8px 25px rgba(0,0,0,0.12)",
      border: `1px solid ${THEME_COLORS.LIGHT_BG}`
    }}>
      {/* En-tête */}
      <div style={{
        textAlign: "center",
        marginBottom: "20px",
        padding: "15px",
        backgroundColor: THEME_COLORS.LIGHT_BG,
        borderRadius: "10px",
        border: `2px solid ${THEME_COLORS.PRIMARY}`
      }}>
        <h5 style={{
          color: THEME_COLORS.PRIMARY,
          marginBottom: "5px",
          fontWeight: "700",
          fontSize: "18px"
        }}>
          🥧 Répartition des Entités
        </h5>
        <p style={{
          color: THEME_COLORS.TEXT_DARK,
          margin: "0",
          fontSize: "13px",
          fontStyle: "italic"
        }}>
          Distribution des ressources du système
        </p>
        {total > 0 && (
          <div style={{
            marginTop: "8px",
            fontSize: "14px",
            fontWeight: "600",
            color: THEME_COLORS.TEXT_DARK
          }}>
            Total: {total} entités
          </div>
        )}
      </div>

      {data.length > 0 ? (
        <>
          {/* Message informatif si toutes les valeurs sont à 0 */}
          {total === 0 && (
            <div className="alert alert-info" style={{
              backgroundColor: THEME_COLORS.LIGHT_BG,
              borderLeft: `4px solid ${THEME_COLORS.PRIMARY}`,
              borderRadius: "0 4px 4px 0",
              marginBottom: "20px",
              fontSize: "14px"
            }}>
              <i className="fa fa-info-circle me-2" style={{ color: THEME_COLORS.PRIMARY }}></i>
              <strong>Information :</strong> Le système ne contient pas encore de données.
            </div>
          )}

          {/* Graphique en secteurs */}
          <ResponsiveContainer width="100%" height={350}>
            <RechartsPieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                outerRadius={100}
                innerRadius={30}
                fill="#8884d8"
                dataKey="value"
                animationBegin={0}
                animationDuration={800}
              >
                {data.map((_, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                    stroke={THEME_COLORS.BACKGROUND}
                    strokeWidth={2}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend
                verticalAlign="bottom"
                height={36}
                formatter={(value, entry) => (
                  <span style={{
                    color: THEME_COLORS.TEXT_DARK,
                    fontWeight: "500",
                    fontSize: "14px"
                  }}>
                    {entry.payload.icon} {value}
                  </span>
                )}
              />
            </RechartsPieChart>
          </ResponsiveContainer>

          {/* Statistiques résumées */}
          <div style={{
            marginTop: "20px",
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
            gap: "10px"
          }}>
            {data.map((item, index) => (
              <div
                key={item.name}
                style={{
                  padding: "12px",
                  backgroundColor: THEME_COLORS.LIGHT_BG,
                  borderRadius: "8px",
                  border: `2px solid ${COLORS[index % COLORS.length]}`,
                  textAlign: "center",
                  fontSize: "12px"
                }}
              >
                <div style={{ fontSize: "20px", marginBottom: "3px" }}>
                  {item.icon}
                </div>
                <div style={{
                  fontSize: "20px",
                  fontWeight: "bold",
                  color: COLORS[index % COLORS.length],
                  marginBottom: "3px"
                }}>
                  {item.value}
                </div>
                <div style={{
                  fontSize: "12px",
                  fontWeight: "600",
                  color: THEME_COLORS.TEXT_DARK
                }}>
                  {item.name}
                </div>
                {total > 0 && (
                  <div style={{
                    fontSize: "10px",
                    color: THEME_COLORS.NEUTRAL,
                    marginTop: "3px"
                  }}>
                    {((item.value / total) * 100).toFixed(1)}%
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      ) : (
        <div style={{
          textAlign: "center",
          padding: "40px",
          color: THEME_COLORS.NEUTRAL
        }}>
          <div style={{
            fontSize: "48px",
            marginBottom: "15px",
            color: THEME_COLORS.PRIMARY,
            opacity: 0.6
          }}>
            🥧
          </div>
          <h6 style={{
            margin: "0 0 8px 0",
            fontSize: "16px",
            color: THEME_COLORS.TEXT_DARK,
            fontWeight: "600"
          }}>
            Aucune donnée disponible
          </h6>
          <p style={{
            margin: "0",
            color: THEME_COLORS.NEUTRAL,
            fontSize: "13px"
          }}>
            Les statistiques ne sont pas encore disponibles.
          </p>
        </div>
      )}
    </div>
  );
};

// ====================================================================
// EXPORT DU COMPOSANT
// ====================================================================

/**
 * EXPORT PAR DÉFAUT DU COMPOSANT PIECHART
 *
 * Ce composant peut être importé et utilisé dans d'autres parties
 * de l'application pour afficher les statistiques du système éducatif
 * sous forme de graphique en secteurs.
 *
 * EXEMPLES D'UTILISATION :
 *
 * 1. Import simple :
 *    import PieChart from './components/dashboard/Pie_chart';
 *    <PieChart />
 *
 * 2. Dans un dashboard :
 *    <div className="col-lg-6">
 *      <PieChart />
 *    </div>
 *
 * 3. Avec gestion d'état parent :
 *    const [refreshKey, setRefreshKey] = useState(0);
 *    <PieChart key={refreshKey} />
 *
 * NOTES IMPORTANTES :
 * - Le composant gère automatiquement ses états internes
 * - Aucune prop n'est requise pour le fonctionnement de base
 * - Les données sont récupérées automatiquement depuis l'API
 * - Le design est responsive et s'adapte à tous les écrans
 * - Les couleurs respectent le design system de l'application
 */
export default PieChart;
