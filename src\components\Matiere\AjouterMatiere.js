import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Form } from "react-bootstrap";
import axiosInstance from "../../services/axiosService";
import ReactSelect from "react-select";

const AjouterMatiere = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    nomMatiere: "",
    description: "",
    duree: "",
    niveauIds: [],
    abonnementIds: [],
  });
  const [niveaux, setNiveaux] = useState([]);
  const [abonnements, setAbonnements] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [niveauxRes, abonnementsRes] = await Promise.all([
          axiosInstance.get("/api/niveaux/all"),
          axiosInstance.get("/api/abonnements/all"),
        ]);
        setNiveaux(niveauxRes.data);
        setAbonnements(abonnementsRes.data);
      } catch (error) {
        console.error("Erreur lors du chargement des données:", error);
        setError("Impossible de charger les données nécessaires");
      }
    };

    fetchData();
  }, []);

  const handleSelectChange = (selectedOptions, field) => {
    const selectedValues = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setFormData({ ...formData, [field]: selectedValues });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError("");
  };

  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("nomMatiere", formData.nomMatiere);
      formDataToSend.append("description", formData.description);
      formDataToSend.append("duree", parseInt(formData.duree, 10));

      formData.niveauIds.forEach((id) => {
        formDataToSend.append("niveauIds", id);
      });

      formData.abonnementIds.forEach((id) => {
        formDataToSend.append("abonnementIds", id);
      });

      if (selectedFile) {
        formDataToSend.append("image", selectedFile);
      }

      const response = await axiosInstance.post("/api/matieres/add", formDataToSend, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      setSuccess("Matière ajoutée avec succès !");
      setTimeout(() => {
        navigate("/matieres");
      }, 2000);
    } catch (error) {
      console.error("Erreur lors de l'ajout de la matière:", error);
      setError(
        error.response?.data?.message || "Erreur lors de l'ajout de la matière"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4 style={{ color: "#37A7DF" }}>Ajouter une matière</h4>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-xl-12 col-lg-12">
          <div className="card">
            <div className="card-body">
              {error && <div className="alert alert-danger">{error}</div>}
              {success && <div className="alert alert-success">{success}</div>}

              <Form.Group className="mb-3">
                <Form.Label>Nom de la matière</Form.Label>
                <Form.Control
                  type="text"
                  name="nomMatiere"
                  value={formData.nomMatiere}
                  onChange={handleInputChange}
                  required
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  required
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Durée (en heures)</Form.Label>
                <Form.Control
                  type="number"
                  name="duree"
                  value={formData.duree}
                  onChange={handleInputChange}
                  required
                  min="1"
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Niveau</Form.Label>
                <ReactSelect
                  isMulti
                  options={niveaux.map((niveau) => ({
                    value: niveau.id,
                    label: niveau.nom,
                  }))}
                  onChange={(selectedOptions) =>
                    handleSelectChange(selectedOptions, "niveauIds")
                  }
                  placeholder="Sélectionner plusieurs niveaux"
                  value={formData.niveauIds.map((id) => ({
                    value: id,
                    label: niveaux.find((niveau) => niveau.id === id)?.nom,
                  }))}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Abonnement</Form.Label>
                <ReactSelect
                  isMulti
                  options={abonnements.map((abonnement) => ({
                    value: abonnement.id,
                    label: abonnement.nom,
                  }))}
                  onChange={(selectedOptions) =>
                    handleSelectChange(selectedOptions, "abonnementIds")
                  }
                  placeholder="Sélectionner plusieurs abonnements"
                  value={formData.abonnementIds.map((id) => ({
                    value: id,
                    label: abonnements.find(
                      (abonnement) => abonnement.id === id
                    )?.nom,
                  }))}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Image</Form.Label>
                <Form.Control
                  type="file"
                  onChange={handleFileChange}
                  accept="image/*"
                />
              </Form.Group>

              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
                style={{
                  backgroundColor: "#37A7DF",
                  borderColor: "#37A7DF",
                }}
                onClick={handleSubmit}
              >
                {loading ? "Ajout en cours..." : "Ajouter la matière"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AjouterMatiere;
