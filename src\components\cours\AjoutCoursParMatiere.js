import React, { useState, useEffect } from "react";
import { useNavigate, usePara<PERSON>, Link } from "react-router-dom";
import { useKeycloak } from "@react-keycloak/web";


const AjoutCoursParMatiere = () => {
  const navigate = useNavigate();
  const { keycloak } = useKeycloak();

  const { idMatiere, id } = useParams(); // ← récupérer les deux
  const [formData, setFormData] = useState({
    titre: "",
    description: "",
    duree: "",
    chapitreId: "",
  });
const [chapitres, setChapitres] = useState([]);
  const [videoFile, setVideoFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");

  const API_COURS = "http://localhost:8084/api/cours";
const API_CHAPITRES = `http://localhost:8084/api/chapitres/matiere/${idMatiere}`;


useEffect(() => {
  const chargerChapitres = async () => {
    try {
      // Vérifier que l'utilisateur est authentifié
      if (!keycloak.authenticated) {
        await keycloak.login();
        return;
      }

     // Récupérer le token directement depuis Keycloak
     const token = keycloak.token;

     const response = await fetch(API_CHAPITRES, {
       method: "GET",
       headers: {
         "Accept": "application/json",
         "Authorization": `Bearer ${token}`,
       },
     });

     if (!response.ok) {
       throw new Error("Échec du chargement des chapitres");
     }

     const data = await response.json();
     setChapitres(data.chapitres || data);
   } catch (error) {
     console.error("Erreur:", error);
     setErrors({ fetch: "Impossible de charger les chapitres" });
   }
 };

 if (idMatiere) {
   chargerChapitres();
 }
}, [idMatiere, keycloak]);

  const handleChange = (e) => {
    const { name, value } = e.target; 
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };
  const handleVideoChange = (e) => {
    setVideoFile(e.target.files[0]);
  };
  const validateForm = () => {
    const nouvellesErreurs = {};
    
    if (!formData.titre.trim()) nouvellesErreurs.titre = "Titre requis";
    if (!formData.description.trim()) nouvellesErreurs.description = "Description requise";
    if (!formData.duree || isNaN(formData.duree)) nouvellesErreurs.duree = "Durée invalide";
    if (!formData.chapitreId) nouvellesErreurs.chapitreId = "Chapitre requis";
    if (videoFile && !videoFile.type.includes("video/")) {
      nouvellesErreurs.video = "Format vidéo invalide";
    }

    setErrors(nouvellesErreurs);
    return Object.keys(nouvellesErreurs).length === 0;
  };
  

  const handleFileChange = (e) => {
    setFormData({
      ...formData,
      pdf: e.target.files[0],
    });
  };
 

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setUploading(true);
    try {
      // Vérifier que l'utilisateur est authentifié
      if (!keycloak.authenticated) {
        await keycloak.login();
        return;
      }

      const newCours = {
        titre: formData.titre,
        description: formData.description,
        duree: parseInt(formData.duree),
        dateCreation: new Date().toISOString(),
        chapitre: {
          id: parseInt(formData.chapitreId)
        }
      };

      const createResponse = await fetch(`${API_COURS}/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "Authorization": `Bearer ${keycloak.token}` // Ajout du token
        },
        body: JSON.stringify(newCours)
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json().catch(() => null);
        throw new Error(errorData?.message || "Erreur lors de la création du cours");
      }

      const createdCours = await createResponse.json();

      // Upload de la vidéo si présente
      if (videoFile) {
        const formDataVideo = new FormData();
        formDataVideo.append("file", videoFile);
        
        const uploadResponse = await fetch(
          `${API_COURS}/uploadVideo/${createdCours.idCours}`,
          {
            method: "POST",
            headers: {
              "Authorization": `Bearer ${keycloak.token}` // Ajout du token
            },
            body: formDataVideo
          }
        );

        if (!uploadResponse.ok) {
          throw new Error("Erreur lors du téléchargement de la vidéo");
        }
      }

      setSuccessMessage("Cours créé avec succès !");
      setTimeout(() => {
        navigate("/cours");
      }, 2000);
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setUploading(false);
    }
  };

  // Rendu du composant
  return (
    
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4 style={{ color: "#37A7DF" }}>Ajouter un cours</h4>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-xl-12 col-xxl-12 col-sm-12">
            <div className="card">
              <div className="card-header">
                <h5 className="card-title">Informations du cours</h5>
              </div>
              <div className="card-body">
                {successMessage && (
                  <div className="alert alert-success">{successMessage}</div>
                )}
                {errors.submit && (
                  <div className="alert alert-danger">{errors.submit}</div>
                )}
                <form onSubmit={handleSubmit}>
                  <div className="row">
                    <div className="col-lg-6 col-md-6 col-sm-12">
                      <div className="form-group">
                        <label className="form-label">Titre</label>
                        <input
                          type="text"
                          name="titre"
                          className={`form-control ${errors.titre ? "is-invalid" : ""}`}
                          value={formData.titre}
                          onChange={handleChange}
                        />
                        {errors.titre && (
                          <div className="invalid-feedback">{errors.titre}</div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-sm-12">
                      <div className="form-group">
                        <label className="form-label">Durée (minutes)</label>
                        <input
                          type="number"
                          name="duree"
                          className={`form-control ${errors.duree ? "is-invalid" : ""}`}
                          value={formData.duree}
                          onChange={handleChange}
                        />
                        {errors.duree && (
                          <div className="invalid-feedback">{errors.duree}</div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-12 col-md-12 col-sm-12">
                      <div className="form-group">
                        <label className="form-label">Description </label>
                        <textarea
                          name="description"
                          className={`form-control ${
                            errors.description ? "is-invalid" : ""
                          }`}
                          value={formData.description}
                          onChange={handleChange}
                        ></textarea>
                        {errors.description && (
                          <div className="invalid-feedback">
                            {errors.description}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-sm-12">
                      <div className="form-group">
                        <label className="form-label">Chapitre</label>
                        <select
                          name="chapitreId"
                          className={`form-control ${
                            errors.chapitreId ? "is-invalid" : ""
                          }`}
                          value={formData.chapitreId}
                          onChange={handleChange}
                        >
                          <option value="">Sélectionner un chapitre</option>
                          {chapitres.map((chapitre) => (
                            <option key={chapitre.id} value={chapitre.id}>
                              {chapitre.nomChapitre}
                            </option>
                          ))}
                        </select>
                        {errors.chapitreId && (
                          <div className="invalid-feedback">
                            {errors.chapitreId}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-sm-12">
                      <div className="form-group">
                        <label className="form-label">Vidéo du cours</label>
                        <input
                          type="file"
                          accept="video/*"
                          className="form-control"
                          onChange={handleVideoChange}
                        />
                      </div>
                    </div>
                    <div className="col-lg-12 col-md-12 col-sm-12">
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={uploading}
                        style={{
                          backgroundColor: "#37A7DF",
                          borderColor: "#37A7DF"
                        }}
                      >
                        {uploading ? "Ajout en cours..." : "Ajouter le cours"}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default AjoutCoursParMatiere;
