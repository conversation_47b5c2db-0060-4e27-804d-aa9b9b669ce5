import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axiosInstance from '../../services/axiosService';
import { Modal, Form, Button, Dropdown } from 'react-bootstrap';

const AfficherEtudiant = () => {
  const navigate = useNavigate();
  const [etudiants, setEtudiants] = useState([]);
  const [abonnements, setAbonnements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedEtudiant, setSelectedEtudiant] = useState(null);
  const [viewMode, setViewMode] = useState('card'); // 'card' or 'table'
  const [editFormData, setEditFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    abonnementIds: []
  });

  useEffect(() => {
    fetchEtudiants();
    fetchAbonnements();
  }, []);

  const fetchAbonnements = async () => {
    try {
      const response = await axiosInstance.get('/api/abonnements/all');
      setAbonnements(response.data);
    } catch (err) {
      console.error('Error fetching abonnements:', err);
      setError('Erreur lors du chargement des abonnements');
    }
  };

  const fetchEtudiants = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get('/api/etudiants');
      setEtudiants(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching etudiants:', err);
      setError('Erreur lors du chargement des étudiants');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedEtudiant) return;

    try {
      await axiosInstance.delete(`/api/etudiants/${selectedEtudiant.id}`);
      setEtudiants(etudiants.filter(e => e.id !== selectedEtudiant.id));
      setShowDeleteModal(false);
      setSelectedEtudiant(null);
    } catch (err) {
      console.error('Error deleting etudiant:', err);
      setError('Erreur lors de la suppression de l\'étudiant');
    }
  };

  const openEditModal = (etudiant) => {
    setSelectedEtudiant(etudiant);
    setEditFormData({
      firstName: etudiant.firstName,
      lastName: etudiant.lastName,
      email: etudiant.email,
      phoneNumber: etudiant.phoneNumber,
      abonnementIds: etudiant.abonnements?.map(a => a.id) || []
    });
    setShowEditModal(true);
  };

  const handleEdit = async () => {
    try {
      const response = await axiosInstance.put(`/api/etudiants/${selectedEtudiant.id}`, editFormData);

      setEtudiants(etudiants.map(etudiant =>
        etudiant.id === selectedEtudiant.id ? response.data : etudiant
      ));

      setShowEditModal(false);
      setSelectedEtudiant(null);
      setError(null);
    } catch (err) {
      console.error('Error updating etudiant:', err);
      setError('Erreur lors de la modification de l\'étudiant');
    }
  };

  const openDeleteModal = (etudiant) => {
    setSelectedEtudiant(etudiant);
    setShowDeleteModal(true);
  };

  return (
      <div className="container-fluid">
        <div className="row page-titles mx-0 mb-4">
          <div className="col-sm-6 p-md-0">
            <h4 style={{ color: "var(--primary-blue)", fontWeight: '500', fontSize: '1.8rem' }}>Liste des Étudiants</h4>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex align-items-center">
            <div className="view-toggle me-3">
              <div className="btn-group" role="group" aria-label="View mode">
                <button
                  type="button"
                  className={`btn ${viewMode === 'card' ? 'btn-primary' : 'btn-outline-primary'}`}
                  style={{
                    backgroundColor: viewMode === 'card' ? 'var(--primary-blue)' : 'transparent',
                    borderColor: 'var(--primary-blue)',
                    color: viewMode === 'card' ? 'white' : 'var(--primary-blue)',
                    borderRadius: '6px 0 0 6px',
                    padding: '8px 15px',
                  }}
                  onClick={() => setViewMode('card')}
                >
                  <i className="la la-th-large me-1"></i> Cartes
                </button>
                <button
                  type="button"
                  className={`btn ${viewMode === 'table' ? 'btn-primary' : 'btn-outline-primary'}`}
                  style={{
                    backgroundColor: viewMode === 'table' ? 'var(--primary-blue)' : 'transparent',
                    borderColor: 'var(--primary-blue)',
                    color: viewMode === 'table' ? 'white' : 'var(--primary-blue)',
                    borderRadius: '0 6px 6px 0',
                    padding: '8px 15px',
                  }}
                  onClick={() => setViewMode('table')}
                >
                  <i className="la la-table me-1"></i> Tableau
                </button>
              </div>
            </div>
            <button
              style={{
                backgroundColor: "var(--primary-blue)",
                borderColor: "var(--primary-blue)",
                color: "white",
                borderRadius: "6px",
                padding: "8px 15px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              className="btn"
              onClick={() => navigate("/ajouter-etudiant")}
            >
              <i className="la la-plus-circle me-2"></i> Ajouter un Étudiant
            </button>
          </div>
        </div>

        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {loading ? (
          <div className="text-center my-5">
            <div className="spinner-border" style={{ color: 'var(--primary-blue)', width: '3rem', height: '3rem' }} role="status">
              <span className="visually-hidden">Chargement...</span>
            </div>
            <p className="mt-3 text-muted">Chargement des données...</p>
          </div>
        ) : (
          <>
            {/* Card View */}
            {viewMode === 'card' && (
              <div className="row g-3">
                {etudiants.map((etudiant) => (
                  <div key={etudiant.id} className="col-xl-4 col-xxl-4 col-lg-6 col-md-6">
                    <div className="card h-100 shadow-sm border-0" style={{ borderRadius: '12px', overflow: 'hidden' }}>
                      <div className="card-header" style={{
                        backgroundColor: 'var(--primary-blue)',
                        color: 'white',
                        borderBottom: 'none',
                        padding: '15px 20px'
                      }}>
                        <div className="d-flex justify-content-between align-items-center">
                          <h5 className="mb-0" style={{ fontWeight: '600' }}>
                            <i className="la la-user-graduate me-2"></i>
                            {etudiant.firstName} {etudiant.lastName}
                          </h5>
                          <div>
                            <button
                              className="btn btn-sm me-1"
                              style={{
                                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                border: 'none',
                                color: 'white',
                                borderRadius: '6px',
                                width: '32px',
                                height: '32px'
                              }}
                              onClick={() => openEditModal(etudiant)}
                              title="Modifier"
                            >
                              <i className="la la-edit"></i>
                            </button>
                            <button
                              className="btn btn-sm"
                              style={{
                                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                border: 'none',
                                color: 'white',
                                borderRadius: '6px',
                                width: '32px',
                                height: '32px'
                              }}
                              onClick={() => openDeleteModal(etudiant)}
                              title="Supprimer"
                            >
                              <i className="la la-trash"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="card-body p-4">
                        <div className="student-info">
                          <div className="mb-3 d-flex align-items-center">
                            <div style={{
                              width: '36px',
                              height: '36px',
                              backgroundColor: 'rgba(55, 167, 223, 0.1)',
                              borderRadius: '50%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginRight: '12px'
                            }}>
                              <i className="la la-envelope" style={{ color: 'var(--primary-blue)', fontSize: '1.2rem' }}></i>
                            </div>
                            <div>
                              <div className="text-muted small">Email</div>
                              <div>{etudiant.email}</div>
                            </div>
                          </div>

                          <div className="mb-3 d-flex align-items-center">
                            <div style={{
                              width: '36px',
                              height: '36px',
                              backgroundColor: 'rgba(55, 167, 223, 0.1)',
                              borderRadius: '50%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              marginRight: '12px'
                            }}>
                              <i className="la la-phone" style={{ color: 'var(--primary-blue)', fontSize: '1.2rem' }}></i>
                            </div>
                            <div>
                              <div className="text-muted small">Téléphone</div>
                              <div>{etudiant.phoneNumber}</div>
                            </div>
                          </div>

                          <div className="mb-3">
                            <div style={{
                              display: "flex",
                              alignItems: "center",
                              marginBottom: "8px",
                              color: "var(--primary-green)",
                              fontWeight: "600"
                            }}>
                              <i className="la la-tag me-2"></i>
                              <span>Abonnements</span>
                            </div>
                            <div className="d-flex flex-wrap">
                              {etudiant.abonnements && etudiant.abonnements.length > 0 ? (
                                etudiant.abonnements.map(abonnement => (
                                  <span
                                    key={abonnement.id}
                                    style={{
                                      backgroundColor: "var(--primary-green)",
                                      color: "white",
                                      padding: "3px 8px",
                                      borderRadius: "15px",
                                      fontSize: "0.75rem",
                                      marginRight: "5px",
                                      marginBottom: "5px",
                                      display: "inline-block"
                                    }}
                                  >
                                    {abonnement.nom}
                                  </span>
                                ))
                              ) : (
                                <span style={{
                                  backgroundColor: "#f0f0f0",
                                  color: "#666",
                                  padding: "3px 8px",
                                  borderRadius: "15px",
                                  fontSize: "0.75rem",
                                  marginBottom: "5px",
                                  display: "inline-block"
                                }}>Aucun abonnement</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Table View */}
            {viewMode === 'table' && (
              <div className="card shadow-sm border-0" style={{ borderRadius: '12px', overflow: 'hidden' }}>
                <div className="card-body p-0">
                  <div className="table-responsive">
                    <table className="table table-hover mb-0">
                      <thead style={{ backgroundColor: 'var(--primary-blue)', color: 'white' }}>
                        <tr>
                          <th style={{ padding: '15px 20px' }}>Nom</th>
                          <th style={{ padding: '15px 20px' }}>Email</th>
                          <th style={{ padding: '15px 20px' }}>Téléphone</th>
                          <th style={{ padding: '15px 20px' }}>Abonnements</th>
                          <th style={{ padding: '15px 20px', width: '120px' }}>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {etudiants.map((etudiant) => (
                          <tr key={etudiant.id}>
                            <td style={{ padding: '15px 20px', verticalAlign: 'middle' }}>
                              <div className="d-flex align-items-center">
                                <div style={{
                                  width: '40px',
                                  height: '40px',
                                  backgroundColor: 'rgba(55, 167, 223, 0.1)',
                                  borderRadius: '50%',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  marginRight: '12px'
                                }}>
                                  <i className="la la-user" style={{ color: 'var(--primary-blue)', fontSize: '1.5rem' }}></i>
                                </div>
                                <div>
                                  <div style={{ fontWeight: '600' }}>{etudiant.firstName} {etudiant.lastName}</div>
                                </div>
                              </div>
                            </td>
                            <td style={{ padding: '15px 20px', verticalAlign: 'middle' }}>{etudiant.email}</td>
                            <td style={{ padding: '15px 20px', verticalAlign: 'middle' }}>{etudiant.phoneNumber}</td>
                            <td style={{ padding: '15px 20px', verticalAlign: 'middle' }}>
                              {etudiant.abonnements && etudiant.abonnements.length > 0 ? (
                                <div className="d-flex flex-wrap">
                                  {etudiant.abonnements.map(abonnement => (
                                    <span
                                      key={abonnement.id}
                                      style={{
                                        backgroundColor: "var(--primary-green)",
                                        color: "white",
                                        padding: "3px 8px",
                                        borderRadius: "15px",
                                        fontSize: "0.75rem",
                                        marginRight: "5px",
                                        marginBottom: "5px",
                                        display: "inline-block"
                                      }}
                                    >
                                      {abonnement.nom}
                                    </span>
                                  ))}
                                </div>
                              ) : (
                                <span style={{
                                  backgroundColor: "#f0f0f0",
                                  color: "#666",
                                  padding: "3px 8px",
                                  borderRadius: "15px",
                                  fontSize: "0.75rem",
                                  display: "inline-block"
                                }}>Aucun abonnement</span>
                              )}
                            </td>
                            <td style={{ padding: '15px 20px', verticalAlign: 'middle' }}>
                              <Dropdown>
                                <Dropdown.Toggle variant="link" className="p-0" style={{ color: 'var(--primary-blue)' }}>
                                  <i className="la la-ellipsis-v" style={{ fontSize: '1.2rem' }}></i>
                                </Dropdown.Toggle>
                                <Dropdown.Menu>
                                  <Dropdown.Item onClick={() => openEditModal(etudiant)}>
                                    <i className="la la-edit text-primary me-2"></i>Modifier
                                  </Dropdown.Item>
                                  <Dropdown.Item onClick={() => openDeleteModal(etudiant)}>
                                    <i className="la la-trash text-danger me-2"></i>Supprimer
                                  </Dropdown.Item>
                                </Dropdown.Menu>
                              </Dropdown>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {etudiants.length === 0 && (
              <div className="text-center my-5">
                <div style={{
                  width: '80px',
                  height: '80px',
                  backgroundColor: 'rgba(55, 167, 223, 0.1)',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 20px auto'
                }}>
                  <i className="la la-user-slash" style={{ color: 'var(--primary-blue)', fontSize: '2.5rem' }}></i>
                </div>
                <h5>Aucun étudiant trouvé</h5>
                <p className="text-muted">Ajoutez des étudiants pour les voir apparaître ici.</p>
              </div>
            )}
          </>
        )}

        {/* Delete Confirmation Modal */}
        <Modal
          show={showDeleteModal}
          onHide={() => setShowDeleteModal(false)}
          centered
          size="sm"
          contentClassName="border-0 shadow"
          backdropClassName="bg-dark bg-opacity-75"
        >
          <Modal.Header
            closeButton
            style={{
              backgroundColor: "var(--primary-navy)",
              color: "white",
              border: "none",
              borderTopLeftRadius: "8px",
              borderTopRightRadius: "8px"
            }}
          >
            <Modal.Title className="fs-5">
              <i className="la la-exclamation-triangle me-2"></i>
              Confirmer la suppression
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: "20px" }}>
            <div className="text-center mb-3">
              <div style={{
                width: "60px",
                height: "60px",
                borderRadius: "50%",
                backgroundColor: "rgba(220, 53, 69, 0.1)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                margin: "0 auto 15px auto"
              }}>
                <i className="la la-trash" style={{ fontSize: "2rem", color: "#dc3545" }}></i>
              </div>
            </div>
            <p className="mb-0 text-center" style={{ fontSize: "1rem" }}>
              Êtes-vous sûr de vouloir supprimer l'étudiant{" "}
              <strong style={{ color: "var(--primary-blue)" }}>{selectedEtudiant?.firstName} {selectedEtudiant?.lastName}</strong> ?
            </p>
            <p className="text-muted text-center mt-2 small">Cette action est irréversible.</p>
          </Modal.Body>
          <Modal.Footer className="justify-content-center border-0" style={{ padding: "0 20px 20px 20px" }}>
            <Button
              style={{
                backgroundColor: "transparent",
                borderColor: "#6c757d",
                color: "#6c757d",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                marginRight: "10px"
              }}
              onClick={() => setShowDeleteModal(false)}
              className="w-100 w-sm-auto"
            >
              <i className="la la-times me-2"></i> Annuler
            </Button>
            <Button
              style={{
                backgroundColor: "#dc3545",
                borderColor: "#dc3545",
                color: "white",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              onClick={handleDelete}
              className="w-100 w-sm-auto mt-2 mt-sm-0"
            >
              <i className="la la-trash me-2"></i> Supprimer
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Edit Modal */}
        <Modal
          show={showEditModal}
          onHide={() => setShowEditModal(false)}
          size="lg"
          contentClassName="border-0 shadow"
          backdropClassName="bg-dark bg-opacity-75"
        >
          <Modal.Header
            closeButton
            style={{
              backgroundColor: "var(--primary-blue)",
              color: "white",
              border: "none",
              padding: "15px 20px"
            }}
          >
            <Modal.Title style={{ fontWeight: "600" }}>
              <i className="la la-edit me-2"></i>
              Modifier l'étudiant
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ padding: "20px" }}>
            <Form>
              <div className="row">
                <div className="col-md-6">
                  <Form.Group className="mb-3">
                    <Form.Label>Prénom</Form.Label>
                    <Form.Control
                      type="text"
                      value={editFormData.firstName}
                      onChange={(e) => setEditFormData({...editFormData, firstName: e.target.value})}
                      className="form-control"
                      style={{ padding: "8px", borderRadius: "6px" }}
                    />
                  </Form.Group>
                </div>
                <div className="col-md-6">
                  <Form.Group className="mb-3">
                    <Form.Label>Nom</Form.Label>
                    <Form.Control
                      type="text"
                      value={editFormData.lastName}
                      onChange={(e) => setEditFormData({...editFormData, lastName: e.target.value})}
                      className="form-control"
                      style={{ padding: "8px", borderRadius: "6px" }}
                    />
                  </Form.Group>
                </div>
              </div>

              <Form.Group className="mb-3">
                <Form.Label>Email</Form.Label>
                <Form.Control
                  type="email"
                  value={editFormData.email}
                  onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Téléphone</Form.Label>
                <Form.Control
                  type="tel"
                  value={editFormData.phoneNumber}
                  onChange={(e) => setEditFormData({...editFormData, phoneNumber: e.target.value})}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px" }}
                />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Abonnements</Form.Label>
                <Form.Select
                  multiple
                  value={editFormData.abonnementIds}
                  onChange={(e) => setEditFormData({
                    ...editFormData,
                    abonnementIds: Array.from(e.target.selectedOptions, option => Number(option.value))
                  })}
                  className="form-control"
                  style={{ padding: "8px", borderRadius: "6px", minHeight: "120px" }}
                >
                  {abonnements.map(abonnement => (
                    <option key={abonnement.id} value={abonnement.id}>
                      {abonnement.nom} - {abonnement.prix} DT
                    </option>
                  ))}
                </Form.Select>
                <small className="text-muted">Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs abonnements</small>
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer style={{
            borderTop: "1px solid #eee",
            padding: "15px 20px",
            display: "flex",
            justifyContent: "space-between"
          }}>
            <Button
              style={{
                backgroundColor: "transparent",
                borderColor: "#dc3545",
                color: "#dc3545",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease"
              }}
              onClick={() => setShowEditModal(false)}
            >
              <i className="la la-times me-2"></i> Annuler
            </Button>
            <Button
              style={{
                backgroundColor: "var(--primary-blue)",
                borderColor: "var(--primary-blue)",
                color: "white",
                borderRadius: "6px",
                padding: "8px 20px",
                fontWeight: "500",
                transition: "all 0.3s ease",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}
              onClick={handleEdit}
            >
              <i className="la la-save me-2"></i> Enregistrer
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
  );
};

export default AfficherEtudiant;
