import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';

const PoserQuestion = () => {
  const { keycloak } = useKeycloak();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    titre: '',
    contenu: '',
    matiereId: ''
  });
  const [matieres, setMatieres] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [userId, setUserId] = useState(null);

  useEffect(() => {
    const fetchMatieres = async () => {
      try {
        // Utiliser l'endpoint correct pour récupérer toutes les matières
        const response = await axiosInstance.get('/api/matieres');
        setMatieres(response.data);
      } catch (err) {
        console.error('Erreur lors du chargement des matières:', err);
        setError('Erreur lors du chargement des matières. Veuillez réessayer plus tard.');
      }
    };

    const fetchUserInfo = async () => {
      if (keycloak.authenticated) {
        try {
          // Récupérer les informations de l'utilisateur depuis Keycloak
          const userInfo = await keycloak.loadUserInfo();
          console.log("Informations utilisateur Keycloak:", userInfo);

          // Utiliser le nom d'utilisateur Keycloak (preferred_username) comme identifiant
          setUserId(userInfo.preferred_username);
        } catch (err) {
          console.error('Erreur lors de la récupération des informations utilisateur:', err);
          setError('Erreur lors de la récupération des informations utilisateur.');
        }
      }
    };

    fetchMatieres();
    fetchUserInfo();
  }, [keycloak]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.titre.trim() || !formData.contenu.trim()) {
      setError('Veuillez remplir tous les champs obligatoires.');
      return;
    }

    if (!userId) {
      setError('Utilisateur non identifié. Veuillez vous reconnecter.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const questionData = {
        titre: formData.titre,
        contenu: formData.contenu
      };

      const params = new URLSearchParams();
      params.append('userId', userId);
      if (formData.matiereId) {
        params.append('matiereId', formData.matiereId);
      }

      const response = await axiosInstance.post(`/api/forum/questions?${params.toString()}`, questionData);

      // Rediriger vers la page de détail de la question
      navigate(`/forum/question/${response.data.id}`);
    } catch (err) {
      console.error('Erreur lors de la création de la question:', err);
      setError('Erreur lors de la création de la question. Veuillez réessayer plus tard.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container-fluid">
    <div className="row page-titles mx-0" style={{  borderRadius: "8px", marginBottom: "20px" }}>
    <div className="col-sm-6 p-md-0">
          <div className="welcome-text">
            <h4 style={{ color: "#000080", fontWeight: "bold" }}>Poser une question</h4>
            <p className="mb-0" style={{ color: "#1D1D1B" }} >Partagez votre question avec la communauté</p>
          </div>
        </div>
        <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
          <ol className="breadcrumb">
            <li className="breadcrumb-item"><Link to="/dashboard">Accueil</Link></li>
            <li className="breadcrumb-item"><Link to="/forum">Forum</Link></li>
            <li className="breadcrumb-item active"><Link to="/forum/poser-question" style={{ color: "#000080" }}> Poser une question</Link></li>
          </ol>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-12">
          <div className="card">
          <div className="card-header" style={{ backgroundColor: "#37A7DF", borderBottom: "3px solid #F2BC00" }}>
          <h4 className="card-title" style={{fontWeight: "600"}}>Nouvelle question</h4>
          </div>
          <div className="card-body" style={{ padding: "25px" }}>
              {error && (
                <div className="alert alert-danger" role="alert" style={{ borderLeft: "4px solid #dc3545" }}>
                  {error}
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className="form-group">
                <label htmlFor="titre" style={{ color: "#1D1D1B", fontWeight: "500" }}>Titre de la question <span style={{ color: "#F2BC00", fontWeight: "bold" }}>*</span></label>
                  <input
                    type="text"
                    className="form-control"
                    id="titre"
                    name="titre"
                    value={formData.titre}
                    onChange={handleChange}
                    placeholder="Soyez précis et imaginez que vous posez une question à une autre personne"
                    required
                    style={{ borderColor: "#B7B7B7", borderRadius: "6px", padding: "10px", backgroundColor: "white" }}

                  />
                </div>
                <div className="form-group">
                <label htmlFor="contenu" style={{ color: "#1D1D1B", fontWeight: "500" }}>Contenu de la question <span style={{ color: "#F2BC00", fontWeight: "bold" }}>*</span></label>
                  <textarea
                    className="form-control"
                    id="contenu"
                    name="contenu"
                    value={formData.contenu}
                    onChange={handleChange}
                    rows="10"
                    placeholder="Incluez tous les détails nécessaires pour que quelqu'un puisse répondre à votre question"
                    required
                    style={{ borderColor: "#B7B7B7", borderRadius: "6px", padding: "10px", backgroundColor: "white" }}
                  ></textarea>
                </div>
                <div className="form-group">
                <label htmlFor="matiereId" style={{ color: "#1D1D1B", fontWeight: "500" }}>Matière <span style={{ color: "#B7B7B7", fontStyle: "italic", fontWeight: "normal" }}>(optionnel)</span></label>
                  <select
                    className="form-control"
                    id="matiereId"
                    name="matiereId"
                    value={formData.matiereId}
                    onChange={handleChange}
                    style={{ borderColor: "#B7B7B7", borderRadius: "6px", padding: "10px", backgroundColor: "white" }}
                  >
                    <option value="">Sélectionnez une matière</option>
                    {matieres.map((matiere) => (
                      <option key={matiere.id} value={matiere.id}>
                        {matiere.nomMatiere}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="form-group" style={{ marginTop: "30px" }}>
                <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                    style={{
                      backgroundColor: "#37A7DF",
                      borderColor: "#37A7DF",
                      padding: "10px 20px",
                      borderRadius: "6px",
                      fontWeight: "500",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      transition: "all 0.3s ease"
                    }}
                >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                        Publication en cours...
                      </>
                    ) : (
                      'Publier la question'
                    )}
                  </button>
                  <Link to="/forum" className="btn btn-light ml-2" style={{
                    
                      padding: "10px 20px",
                      borderRadius: "6px",
                      fontWeight: "500",
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      transition: "all 0.3s ease"
                    }}>Annuler</Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PoserQuestion;
