/* LessonPage.css */

.wpo-lesson-section {
    background-color: #fff;
}

.wpo-lesson-section .container-fluid {
    padding: 0;
}

.wpo-lesson-section .col {
    padding: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar {
    height: 100%;
    border-right: 1px solid #e1e1e1;
    background-color: #f6f4ee;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item {
    border: 0;
    border: 1px solid #e1e1e1;
    border-top: 0;
    border-right: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .MuiAccordionSummary-root {
    padding: 15px 20px;
    border: 0;
    border-radius: 0;
    color: #fff;
    text-align: left;
    background: #000080;
    display: flex;
    align-items: center;
    min-height: 64px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .MuiAccordionSummary-root.Mui-expanded {
    background: #000080;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .MuiAccordionSummary-root::after {
    content: '';
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #fff;
    position: absolute;
    right: 20px;
    transition: transform 0.3s ease;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .MuiAccordionSummary-root.Mui-expanded::after {
    transform: rotate(180deg);
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-title {
    font-size: 18px;
    font-weight: 700;
    color: #fff;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .course-count {
    font-size: 14px;
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 12px;
    margin-left: 10px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .MuiAccordionSummary-root.Mui-expanded {
    min-height: 64px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .MuiAccordionSummary-content.Mui-expanded {
    margin: 12px 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body {
    padding: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item {
    list-style: none;
    padding: 0;
    margin: 0;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li {
    color: #1D1D1B;
    margin-bottom: 0;
    border-bottom: 1px solid #e1e1e1;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a {
    display: flex;
    justify-content: space-between;
    color: #1D1D1B;
    padding: 15px 20px;
    position: relative;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li:last-child a {
    border-bottom: none;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a:hover {
    background-color: rgba(55, 167, 223, 0.1);
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a.active {
    background-color: rgba(55, 167, 223, 0.2);
    border-left: 4px solid #37A7DF;
    font-weight: 600;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a i.flaticon-play-button {
    width: 24px;
    height: 24px;
    line-height: 22px;
    border: 1px solid #F2BC00;
    display: inline-block;
    text-align: center;
    border-radius: 50%;
    color: #F2BC00;
    margin-right: 10px;
    font-size: 12px;
    margin-left: 10px;
    background-color: rgba(242, 188, 0, 0.1);
    transition: all 0.3s ease;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a:hover i.flaticon-play-button {
    background-color: #F2BC00;
    color: #fff;
    transform: scale(1.1);
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a span {
    display: flex;
    align-items: center;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a span:first-child {
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a span:last-child {
    font-size: 14px;
    color: #666;
    min-width: 80px;
    text-align: right;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a span i {
    margin-left: 10px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a .fa-check-circle {
    color: #248E39;
    font-size: 16px;
}

.wpo-lesson-section .wpo-lesson-sidebar .accordion-item .accordion-body .item li a .fa-circle-thin {
    color: #B7B7B7;
    font-size: 16px;
}

.wpo-lesson-section .video-area {
    height: 100%;
}

.wpo-lesson-section .video-area .video-heading {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
    background: #000080;
    align-items: center;
    flex-wrap: wrap;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.wpo-lesson-section .video-area .video-heading h2 {
    font-size: 22px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.wpo-lesson-section .video-area .video-heading .button-group {
    display: flex;
    gap: 10px;
}

.wpo-lesson-section .video-area .video-heading .theme-btn {
    padding: 10px 20px;
    background-color: #F2BC00;
    color: #000080;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wpo-lesson-section .video-area .video-heading .theme-btn.secondary {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.wpo-lesson-section .video-area .video-heading .theme-btn:hover {
    background-color: #fff;
    color: #000080;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.wpo-lesson-section .video-area .video-heading .theme-btn.secondary:hover {
    background-color: rgba(255, 255, 255, 0.3);
    color: #fff;
}

.wpo-lesson-section .video-area video {
    width: 100%;
    background-color: #000;
}

.wpo-lesson-section .video-area .video-details {
    max-width: 100%;
    padding: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin: 20px 0;
}

.wpo-lesson-section .video-area .video-details h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #000080;
    border-bottom: 2px solid #F2BC00;
    padding-bottom: 10px;
    display: inline-block;
}

.wpo-lesson-section .video-area .video-details p {
    font-size: 16px;
    line-height: 1.8;
    color: #1D1D1B;
    margin-bottom: 20px;
}

.wpo-lesson-section .video-area .video-details .mt-4 h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #000080;
}

.wpo-lesson-section .video-area .video-details .btn-outline-primary {
    color: #37A7DF;
    border-color: #37A7DF;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.wpo-lesson-section .video-area .video-details .btn-outline-primary:hover {
    background-color: #37A7DF;
    color: #fff;
}

.wpo-lesson-section .video-area .video-details-pagination {
    padding: 20px;
    background: #EEF9F5;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.wpo-lesson-section .video-area .video-details-pagination ul {
    display: flex;
    justify-content: space-between;
    list-style: none;
    padding: 0;
    margin: 0;
}

.wpo-lesson-section .video-area .video-details-pagination ul li {
    margin: 0;
    flex: 1;
    text-align: center;
}

.wpo-lesson-section .video-area .video-details-pagination ul li:first-child {
    text-align: left;
}

.wpo-lesson-section .video-area .video-details-pagination ul li:last-child {
    text-align: right;
}

.wpo-lesson-section .video-area .video-details-pagination ul li a {
    display: inline-block;
    padding: 12px 30px;
    background: #37A7DF;
    color: #fff;
    border-radius: 30px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wpo-lesson-section .video-area .video-details-pagination ul li a:hover {
    background: #248E39;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.wpo-lesson-section .video-area .video-details-pagination ul li .disabled {
    display: inline-block;
    padding: 12px 30px;
    background: #B7B7B7;
    color: #fff;
    border-radius: 30px;
    cursor: not-allowed;
    font-weight: 600;
    opacity: 0.7;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f6f4ee;
}

.loading-container .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #37A7DF;
}

.error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 30px;
    background-color: #f6f4ee;
    text-align: center;
}

.error-container .alert {
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
}

.error-container .btn {
    padding: 10px 25px;
    background-color: #37A7DF;
    color: #fff;
    border: none;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.error-container .btn:hover {
    background-color: #248E39;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.video-fallback {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    background-color: #f6f4ee;
    border-radius: 8px;
    margin: 20px 0;
}

.video-fallback .btn {
    padding: 15px 30px;
    background-color: #37A7DF;
    color: #fff;
    border: none;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    font-size: 18px;
}

.video-fallback .btn:hover {
    background-color: #248E39;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.no-video-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    background-color: #f6f4ee;
    color: #1D1D1B;
    border-radius: 8px;
    margin: 20px 0;
    font-size: 18px;
    font-weight: 500;
}

/* Responsive styles */
@media (max-width: 991px) {
    .wpo-lesson-section .wpo-lesson-sidebar {
        height: auto;
    }

    .wpo-lesson-section .video-area {
        height: auto;
    }

    .wpo-lesson-section .video-area .video-heading {
        flex-direction: column;
        align-items: flex-start;
    }

    .wpo-lesson-section .video-area .video-heading h2 {
        margin-bottom: 10px;
    }
}

@media (max-width: 767px) {
    .wpo-lesson-section .video-area .video-details {
        padding: 20px;
    }

    .wpo-lesson-section .video-area .video-details h2 {
        font-size: 20px;
    }

    .wpo-lesson-section .video-area .video-details-pagination ul li a,
    .wpo-lesson-section .video-area .video-details-pagination ul li .disabled {
        padding: 8px 20px;
    }
}
