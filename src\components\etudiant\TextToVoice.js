/**
 * Text to Speech Component using ElevenLabs API with Multilingual Support
 *
 * This component uses the ElevenLabs API to convert text to speech in multiple languages:
 * - English
 * - German (Deutsch)
 * - French (Français)
 *
 * API Key: ***************************************************
 *
 * The component uses ElevenLabs' multilingual model (eleven_multilingual_v2) to support
 * multiple languages with the same voice. Each voice can speak in different languages
 * with the appropriate accent.
 *
 * IMPORTANT: We're using the default ElevenLabs voices with the multilingual model.
 * For better language-specific voices, you can:
 * 1. Create your own ElevenLabs account
 * 2. Clone voices or create custom voices for specific languages
 * 3. Update the API key and voice IDs in this component
 */

import React, { useState, useEffect, useRef } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import axiosInstance from '../../services/axiosService';
import axios from 'axios';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';

const TextToVoice = () => {
  const { keycloak } = useKeycloak();
  const [userId, setUserId] = useState(null);
  const [text, setText] = useState('');
  const [voice, setVoice] = useState('21m00Tcm4TlvDq8ikWAM'); // Default voice (Rachel - English)
  const [audioUrl, setAudioUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [history, setHistory] = useState([]);
  const audioRef = useRef(null);

  // Available ElevenLabs voices - Using multilingual voices for English, German, and French
  const voices = [
    // English voices (default voices from ElevenLabs)
    { id: '21m00Tcm4TlvDq8ikWAM', name: 'Rachel (English - Female)', language: 'en' },
    { id: 'AZnzlk1XvdvUeBnXmlld', name: 'Domi (English - Female)', language: 'en' },
    { id: 'EXAVITQu4vr4xnSDxMaL', name: 'Bella (English - Female)', language: 'en' },
    { id: 'ErXwobaYiN019PkySvjV', name: 'Antoni (English - Male)', language: 'en' },
    { id: 'MF3mGyEYCl7XYWbV9V6O', name: 'Elli (English - Female)', language: 'en' },
    { id: 'TxGEqnHWrfWFTfGW9XjX', name: 'Josh (English - Male)', language: 'en' },

    // Multilingual voices that support German (Deutsch)
    { id: 'VR6AewLTigWG4xSOukaG', name: 'Arnold (Deutsch - Male)', language: 'de' },
    { id: 'pNInz6obpgDQGcFmaJgB', name: 'Adam (Deutsch - Male)', language: 'de' },
    { id: 'yoZ06aMxZJJ28mfd3POQ', name: 'Sam (Deutsch - Male)', language: 'de' },

    // Multilingual voices that support French
    { id: 'VR6AewLTigWG4xSOukaG', name: 'Arnold (Français - Homme)', language: 'fr' },
    { id: 'pNInz6obpgDQGcFmaJgB', name: 'Adam (Français - Homme)', language: 'fr' },
    { id: 'yoZ06aMxZJJ28mfd3POQ', name: 'Sam (Français - Homme)', language: 'fr' }
  ];

  // Get user ID on component mount
  useEffect(() => {
    if (keycloak.authenticated) {
      const fetchEtudiantId = async () => {
        try {
          const { preferred_username: username, email } = keycloak.tokenParsed;

          const response = await axiosInstance.get('/api/etudiants');
          const etudiant = response.data.find(
            (e) => e.username === username || e.email === email
          );

          if (etudiant) {
            setUserId(etudiant.id || etudiant.idEtudiant);
            // Fetch history after getting user ID
            fetchHistory(etudiant.id || etudiant.idEtudiant);
          } else {
            if (response.data.length > 0) {
              const firstEtudiant = response.data[0];
              setUserId(firstEtudiant.id || firstEtudiant.idEtudiant);
              fetchHistory(firstEtudiant.id || firstEtudiant.idEtudiant);
            } else {
              setError('Aucun étudiant trouvé dans le système.');
            }
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des étudiants :', error);
          setError('Impossible de récupérer la liste des étudiants.');
        }
      };

      fetchEtudiantId();
    }
  }, [keycloak]);

  // Fetch user's text-to-speech history
  const fetchHistory = async (userId) => {
    try {
      // This is a placeholder - backend endpoint needs to be implemented
      const response = await axiosInstance.get(`/api/text-to-speech/history/${userId}`);
      if (response.data) {
        setHistory(response.data);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      // Don't show error to user, just log it
    }
  };

  // Try to convert text using the backend as a proxy (fallback method)
  const convertTextViaBackend = async () => {
    try {
      // Use our backend as a proxy to avoid CORS issues
      const response = await axiosInstance.post('/api/text-to-speech/convert-proxy', {
        text,
        voiceId: voice,
        userId
      }, {
        responseType: 'blob'
      });

      return response;
    } catch (error) {
      console.error('Fallback conversion failed:', error);
      throw error;
    }
  };

  // Try to convert text directly using the ElevenLabs API
  const convertTextDirect = async () => {
    // ElevenLabs API key
    const apiKey = '***************************************************';

    // ElevenLabs API endpoint
    const elevenLabsEndpoint = 'https://api.elevenlabs.io/v1/text-to-speech';

    try {
      // Note: The language is automatically detected by the multilingual model
      // We're using the voice ID directly in the API call

      // Prepare the request body for ElevenLabs
      const requestBody = {
        text: text.substring(0, 5000), // Limit text length to 5000 characters
        model_id: "eleven_multilingual_v2", // Use multilingual model for language support
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.5
        }
      };

      // Make the API request
      const response = await axios.post(`${elevenLabsEndpoint}/${voice}`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'xi-api-key': apiKey
        },
        responseType: 'blob'
      });

      // Check if the response is an error message (usually JSON, not audio)
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('application/json')) {
        // Convert blob to text to check for error messages
        const errorText = await response.data.text();
        if (errorText.includes('error')) {
          throw new Error('API Error: ' + errorText);
        }
      }

      // If we got here, it's probably valid audio data
      return response;
    } catch (axiosError) {
      console.log('Axios request failed:', axiosError);

      // Check for specific error types
      if (axiosError.response && axiosError.response.data) {
        const errorData = axiosError.response.data;

        // Check for voice not found error
        if (typeof errorData === 'object' && errorData.detail && errorData.detail.status === 'voice_not_found') {
          throw new Error('La voix sélectionnée n\'a pas été trouvée. Veuillez sélectionner une autre voix.');
        }

        // Check for API key issues
        if (axiosError.message && (
            axiosError.message.includes('key') ||
            axiosError.message.includes('API') ||
            axiosError.message.includes('unauthorized') ||
            axiosError.message.includes('authentication')
        )) {
          throw new Error('Problème avec la clé API ElevenLabs. Veuillez contacter l\'administrateur.');
        }
      }

      // For other errors, try the backend proxy
      throw axiosError;
    }
  };

  // Handle text conversion
  const handleConvertText = async (e) => {
    e.preventDefault();

    if (!text.trim()) {
      setError('Veuillez entrer du texte à convertir.');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Try all available methods to get audio
      let audioObtained = false;

      // Method 1: Try direct API call with axios
      try {
        const response = await convertTextDirect();

        // Check if we have a direct URL from the fallback approach
        if (response.directAudioUrl) {
          console.log('Using direct audio URL:', response.directAudioUrl);

          // Revoke any existing object URL to prevent memory leaks
          if (audioUrl && audioUrl.startsWith('blob:')) {
            URL.revokeObjectURL(audioUrl);
          }

          // Use the direct URL
          setAudioUrl(response.directAudioUrl);
          setSuccess('Conversion réussie ! Cliquez sur le bouton "Jouer l\'audio".');
          audioObtained = true;
        } else if (response.data && response.data.size > 100) {
          // Create URL for the audio blob if it's a valid size
          const audioBlob = new Blob([response.data], { type: 'audio/mpeg' });

          // Revoke any existing object URL to prevent memory leaks
          if (audioUrl && audioUrl.startsWith('blob:')) {
            URL.revokeObjectURL(audioUrl);
          }

          const url = URL.createObjectURL(audioBlob);
          console.log('Created audio URL from direct API call:', url);

          // Update the state with the new URL
          setAudioUrl(url);
          setSuccess('Conversion réussie ! Cliquez sur le bouton "Jouer l\'audio".');
          audioObtained = true;
        } else {
          console.log('Direct API call returned invalid data, trying backend proxy');
        }
      } catch (directError) {
        // Check for specific error types
        if (directError.message) {
          // Voice not found error
          if (directError.message.includes('voix sélectionnée n\'a pas été trouvée')) {
            console.error('Voice not found error:', directError);
            setError('La voix sélectionnée n\'a pas été trouvée. Veuillez sélectionner une autre voix.');
            setIsLoading(false);
            return; // Exit early on voice not found issues
          }

          // API key issues
          if (directError.message.includes('API') ||
              directError.message.includes('clé') ||
              directError.message.includes('key')) {
            console.error('API key error:', directError);
            setError('Problème avec la clé API ElevenLabs. Veuillez contacter l\'administrateur.');
            setIsLoading(false);
            return; // Exit early on API key issues
          }
        }

        console.log('Direct API call failed, trying backend proxy:', directError);
      }

      // Method 2: If direct call failed, try backend proxy
      if (!audioObtained) {
        try {
          const response = await convertTextViaBackend();

          if (response.data && response.data.size > 100) {
            // Create URL for the audio blob
            const audioBlob = new Blob([response.data], { type: 'audio/mpeg' });

            // Revoke any existing object URL to prevent memory leaks
            if (audioUrl && audioUrl.startsWith('blob:')) {
              URL.revokeObjectURL(audioUrl);
            }

            const url = URL.createObjectURL(audioBlob);
            console.log('Created audio URL from backend proxy:', url);

            // Update the state with the new URL
            setAudioUrl(url);
            setSuccess('Conversion réussie ! Cliquez sur le bouton "Jouer l\'audio".');
            audioObtained = true;
          } else {
            console.log('Backend proxy returned invalid data, trying direct URL method');
          }
        } catch (proxyError) {
          // Check for specific error types in the proxy response
          if (proxyError.response && proxyError.response.data) {
            const errorData = proxyError.response.data;

            // Check for voice not found error
            if (typeof errorData === 'object' && errorData.message &&
                errorData.message.includes('voix sélectionnée n\'a pas été trouvée')) {
              console.error('Voice not found error from proxy:', proxyError);
              setError('La voix sélectionnée n\'a pas été trouvée. Veuillez sélectionner une autre voix.');
              setIsLoading(false);
              return; // Exit early on voice not found issues
            }

            // Check for API key issues
            if (typeof errorData === 'object' && errorData.message &&
                (errorData.message.includes('API') || errorData.message.includes('clé'))) {
              console.error('API key error from proxy:', proxyError);
              setError('Problème avec la clé API ElevenLabs. Veuillez contacter l\'administrateur.');
              setIsLoading(false);
              return; // Exit early on API key issues
            }
          }

          console.log('Backend proxy failed:', proxyError);
        }
      }

      // Method 3: Last resort - inform the user that both methods failed
      if (!audioObtained) {
        console.error('All conversion methods failed');
        setError('Erreur lors de la conversion. Veuillez réessayer plus tard ou contacter l\'administrateur.');
        return; // Exit early if all methods fail
      }

      // Save to history via backend
      try {
        await axiosInstance.post('/api/text-to-speech/save-history', {
          text,
          voice,
          userId
        });

        // Refresh history
        fetchHistory(userId);
      } catch (historyError) {
        console.error('Erreur lors de la sauvegarde de l\'historique:', historyError);
        // Don't show this error to the user, just log it
      }
    } catch (error) {
      console.error('Erreur lors de la conversion:', error);
      setError('Erreur lors de la conversion du texte en voix. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  // Clean up audio URL on component unmount or when URL changes
  useEffect(() => {
    // Store the current URL to revoke it later if needed
    const currentUrl = audioUrl;

    // Cleanup function
    return () => {
      if (currentUrl) {
        // Revoke the object URL to free up memory
        URL.revokeObjectURL(currentUrl);
      }
    };
  }, [audioUrl]);

  // Test if an audio URL is valid
  const testAudioUrl = (url) => {
    return new Promise((resolve, reject) => {
      // For direct URLs, we can't easily test them, so assume they're valid
      if (url.startsWith('http') && !url.startsWith('blob:')) {
        resolve(true);
        return;
      }

      // For blob URLs, create a temporary audio element to test
      const audio = new Audio();

      // Set up event handlers
      const handleCanPlay = () => {
        cleanup();
        resolve(true);
      };

      const handleError = () => {
        cleanup();
        reject(new Error('Audio URL test failed'));
      };

      const cleanup = () => {
        audio.removeEventListener('canplay', handleCanPlay);
        audio.removeEventListener('error', handleError);
        audio.src = '';
      };

      // Add event listeners
      audio.addEventListener('canplay', handleCanPlay);
      audio.addEventListener('error', handleError);

      // Set timeout to avoid hanging
      setTimeout(() => {
        cleanup();
        // If it times out, we'll still consider it valid and let the main audio element handle any errors
        resolve(true);
      }, 2000);

      // Start loading the audio
      audio.src = url;
      audio.load();
    });
  };

  // Set audio source when URL changes and handle errors
  useEffect(() => {
    if (!audioUrl || !audioRef.current) return;

    // Set up error handler
    const handleAudioError = (e) => {
      console.error("Audio playback error detected:", e);
      setError("Erreur lors de la lecture audio. Veuillez réessayer ou utiliser le bouton de lecture manuel.");

      // For ElevenLabs, we don't have a direct URL fallback option
      // Just log the error and let the user try again
      console.log('Audio playback error detected, user will need to try again');
    };

    // Test the audio URL first
    testAudioUrl(audioUrl)
      .then(() => {
        // If the test passes, set the source on the main audio element
        try {
          audioRef.current.src = audioUrl;
          audioRef.current.load();

          // Add error listener
          audioRef.current.addEventListener('error', handleAudioError);
        } catch (e) {
          console.error("Error setting audio source:", e);
        }
      })
      .catch(error => {
        console.error("Audio URL test failed:", error);
        setError("L'URL audio semble invalide. Veuillez réessayer.");
      });

    // Cleanup function
    return () => {
      if (audioRef.current) {
        // Remove event listener
        audioRef.current.removeEventListener('error', handleAudioError);

        // Clear source to prevent memory leaks
        try {
          audioRef.current.src = '';
          audioRef.current.load();
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    };
  }, [audioUrl, voice, text]);

  return (
    <div className="position-relative">
      <div className="container-fluid px-4" style={{ backgroundColor: "#F6F4EE", minHeight: "100vh" }}>
        <div className="row page-titles mx-0 d-flex align-items-center justify-content-between flex-wrap">
          <div className="col-auto">
            <h4 className="fw-bold" style={{ color: "#000080" }}>
              Convertisseur Texte en Voix (ElevenLabs AI)
            </h4>
          </div>
        </div>

        {error && (
          <Alert variant="danger">
            {error}
            {error.includes('clé API') && (
              <div className="mt-2">
                <p>
                  <strong>Comment résoudre ce problème :</strong>
                </p>
                <ol>
                  <li>Visitez <a href="https://elevenlabs.io/" target="_blank" rel="noopener noreferrer">ElevenLabs</a> pour créer un compte</li>
                  <li>Obtenez une nouvelle clé API depuis votre tableau de bord</li>
                  <li>Remplacez l'ancienne clé API dans le code source</li>
                </ol>
                <p className="mb-0">
                  <small>Note: Le plan gratuit d'ElevenLabs permet de convertir jusqu'à 10,000 caractères par mois.</small>
                </p>
              </div>
            )}
          </Alert>
        )}
        {success && <Alert variant="success">{success}</Alert>}

        <div className="row">
          <div className="col-lg-8 col-md-12 mb-4">
            <Card className="shadow-sm h-100 border-0">
              <Card.Body>
                <Card.Title style={{ color: "#37A7DF" }}>Convertir du texte en voix (English, Deutsch, Français)</Card.Title>
                <Form onSubmit={handleConvertText}>
                  <Form.Group className="mb-3">
                    <Form.Label>Entrez votre texte</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={6}
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      placeholder="Tapez ou collez votre texte ici..."
                      style={{ backgroundColor: "#EEF9F5" }}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Sélectionnez une voix</Form.Label>
                    <Form.Select
                      value={voice}
                      onChange={(e) => setVoice(e.target.value)}
                      style={{ backgroundColor: "#EEF9F5" }}
                    >
                      <optgroup label="English Voices">
                        {voices.filter(v => v.language === 'en').map((v) => (
                          <option key={`${v.id}-${v.language}`} value={v.id}>
                            {v.name}
                          </option>
                        ))}
                      </optgroup>
                      <optgroup label="German Voices (Deutsch)">
                        {voices.filter(v => v.language === 'de').map((v) => (
                          <option key={`${v.id}-${v.language}`} value={v.id}>
                            {v.name}
                          </option>
                        ))}
                      </optgroup>
                      <optgroup label="French Voices (Français)">
                        {voices.filter(v => v.language === 'fr').map((v) => (
                          <option key={`${v.id}-${v.language}`} value={v.id}>
                            {v.name}
                          </option>
                        ))}
                      </optgroup>
                    </Form.Select>
                  </Form.Group>

                  <Button
                    type="submit"
                    className="btn rounded-pill shadow-sm px-4"
                    style={{
                      backgroundColor: "#F2BC00",
                      color: "#1D1D1B",
                      border: "none",
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                        />
                        <span className="ms-2">Conversion en cours...</span>
                      </>
                    ) : (
                      "Convertir en voix"
                    )}
                  </Button>
                </Form>

                <div className="mt-4">
                  <h5>Écoutez le résultat :</h5>
                  {audioUrl ? (
                    <div className="audio-player-container">
                      {/* Use a simple audio element with minimal attributes */}
                      <audio
                        ref={audioRef}
                        controls
                        className="w-100"
                        controlsList="nodownload"
                      >
                        {/* Use blob URL directly without source element */}
                        Your browser does not support the audio element.
                      </audio>

                      <div className="text-center mt-2">
                        <Button
                          size="sm"
                          variant="primary"
                          onClick={() => {
                            try {
                              // Set the src directly on the audio element
                              if (audioRef.current) {
                                // For direct URLs, we might need to open in a new window
                                if (audioUrl.startsWith('http') && !audioUrl.startsWith('blob:')) {
                                  // Try to set the source first
                                  audioRef.current.src = audioUrl;
                                  audioRef.current.load();

                                  // Try to play
                                  const playPromise = audioRef.current.play();
                                  if (playPromise) {
                                    playPromise.catch(e => {
                                      console.log("Direct URL play failed, opening in new window:", e);
                                      // If play fails, open in a new window as fallback
                                      window.open(audioUrl, '_blank');
                                    });
                                  }
                                } else {
                                  // For blob URLs, just set the source and play
                                  audioRef.current.src = audioUrl;
                                  audioRef.current.load();

                                  // Try to play after a short delay
                                  setTimeout(() => {
                                    const playPromise = audioRef.current.play();
                                    if (playPromise) {
                                      playPromise.catch(e => {
                                        console.log("Play failed, user may need to interact first:", e);
                                      });
                                    }
                                  }, 100);
                                }
                              }
                            } catch (e) {
                              console.error("Error playing audio:", e);
                              // Last resort fallback - open in new window
                              try {
                                window.open(audioUrl, '_blank');
                              } catch (windowError) {
                                console.error("Failed to open audio in new window:", windowError);
                              }
                            }
                          }}
                        >
                          Jouer l'audio
                        </Button>
                        {" "}
                        <Button
                          size="sm"
                          variant="outline-secondary"
                          onClick={() => {
                            try {
                              // Handle download based on URL type
                              if (audioUrl.startsWith('http') && !audioUrl.startsWith('blob:')) {
                                // For direct URLs, open in a new window/tab
                                window.open(audioUrl, '_blank');
                              } else {
                                // For blob URLs, use download attribute
                                const a = document.createElement('a');
                                a.href = audioUrl;
                                a.download = 'audio.mp3';
                                document.body.appendChild(a);
                                a.click();
                                document.body.removeChild(a);
                              }
                            } catch (e) {
                              console.error("Error downloading audio:", e);
                              setError("Erreur lors du téléchargement. Veuillez réessayer.");

                              // Fallback - try to open in new window
                              try {
                                window.open(audioUrl, '_blank');
                              } catch (windowError) {
                                console.error("Failed to open audio in new window:", windowError);
                              }
                            }
                          }}
                        >
                          Télécharger
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-muted">Convertissez du texte pour écouter le résultat.</p>
                  )}
                </div>
              </Card.Body>
            </Card>
          </div>

          <div className="col-lg-4 col-md-12 mb-4">
            <Card className="shadow-sm h-100 border-0">
              <Card.Body>
                <Card.Title style={{ color: "#37A7DF" }}>Historique des conversions</Card.Title>
                {history.length > 0 ? (
                  <div className="conversion-history">
                    {history.map((item, index) => (
                      <div key={index} className="history-item mb-3 p-2 border-bottom">
                        <p className="mb-1 text-truncate">
                          <strong>Texte:</strong> {item.text.substring(0, 50)}
                          {item.text.length > 50 ? '...' : ''}
                        </p>
                        <p className="mb-1"><strong>Voix:</strong> {
                          voices.find(v => v.id === item.voice)?.name || item.voice
                        }</p>
                        <p className="mb-1"><strong>Date:</strong> {new Date(item.createdAt).toLocaleString()}</p>
                        <Button
                          size="sm"
                          className="mt-1 mb-2"
                          style={{
                            backgroundColor: "#37A7DF",
                            color: "white",
                            border: "none",
                          }}
                          onClick={() => {
                            // Set the text and voice from history
                            setText(item.text);
                            setVoice(item.voice);

                            // Scroll to the text area
                            document.querySelector('textarea').scrollIntoView({ behavior: 'smooth' });
                          }}
                        >
                          Réutiliser ce texte
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted">Aucune conversion dans l'historique.</p>
                )}
              </Card.Body>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextToVoice;
