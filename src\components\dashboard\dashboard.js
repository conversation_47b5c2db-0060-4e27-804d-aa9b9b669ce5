import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import "../../styles/theme.css";
import MatieresParAbonnementChart from "./MatieresParAbonnementChart";
import Pie<PERSON>hart from "./Pie_chart";   // Import du nouveau PieChart corrigé
import { Chart as ChartJS } from "chart.js"; // Renommez cet import
import CustomChart from "./chart"; // Renommez cet import


const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCours: 0,
    totalAbonnements: 0,
    totalNiveaux: 0,
    totalMatiere: 0,
  });

  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await axiosInstance.get("/api/statistics");
        setStats(response.data);
      } catch (err) {
        setError("Erreur lors du chargement des statistiques.");
      }
    };
    fetchStats();
  }, []);

  const cards = [
    {
      label: "Cours",
      value: stats.totalCours,
      icon: "la-graduation-cap",
      color: "#37A7DF",
    },
    {
      label: "Abonnements",
      value: stats.totalAbonnements,
      icon: "la-book",
      color: "#F2BC00",
    },
    {
      label: "Niveaux",
      value: stats.totalNiveaux,
      icon: "la-layer-group",
      color: "#248E39",
    },
    {
      label: "Matières",
      value: stats.totalMatiere,
      icon: "la-book-open",
      color: "#000080",
    },
  ];

  return (
    <div style={{  minHeight: "100vh", padding: "2rem" }}>
      {error && <div className="alert alert-danger mb-4">{error}</div>}

    {/*  <div className="row g-4">
        {cards.map((card, i) => (
          <div key={i} className="col-12 col-sm-6 col-lg-3">
            <div
              className="card h-100 text-white shadow-sm border-0"
              style={{
                borderRadius: "16px",
                backgroundColor: card.color,
                transition: "all 0.3s ease",
              }}
            >
              <div className="card-body d-flex align-items-center">
                <div
                  className="icon-wrapper me-3"
                  style={{
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                    width: 55,
                    height: 55,
                    borderRadius: "50%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <i className={`la ${card.icon} fs-4 text-white`}></i>
                </div>
                <div>
                  <p className="mb-1" style={{ fontSize: "0.9rem", opacity: "0.85" }}>
                    Total {card.label}
                  </p>
                  <h4 className="mb-0" style={{ fontWeight: "700" }}>
                    {card.value}
                  </h4>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
        <div className="row mt-5">*/}
        <div className="col-12">
          <CustomChart/> {/* Utilisez le nouveau nom ici */}
        </div>

      <div className="row mt-5">
        <div className="col-lg-6 col-12 mb-4">
          <MatieresParAbonnementChart/>
        </div>
        <div className="col-lg-6 col-12 mb-4">
          <PieChart/>
        </div>
      </div>



      <div className="row mt-5">
        <div className="col-12">
          <div
            className="card shadow-sm border-0"
            style={{
              borderLeft: "6px solid #37A7DF",
              backgroundColor: "#F6F4EE",
              borderRadius: "16px",
            }}
          >
            <div className="card-body">
              <h5 className="mb-2" style={{ fontWeight: "600", color: "#1D1D1B" }}>
                👋 Bienvenue sur le tableau de bord
              </h5>
              <p style={{ color: "#B7B7B7", fontSize: "0.95rem" }}>
                Accédez à toutes les fonctionnalités de gestion : cours, abonnements, niveaux, matières et plus encore.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;