import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Badge, Button } from 'react-bootstrap';
import axios from 'axios';
import { useKeycloak } from '@react-keycloak/web';
import Swal from 'sweetalert2';
import Spinner from 'react-bootstrap/Spinner';

const PendingRegistrations = () => {
    const [pendingStudents, setPendingStudents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [processingIds, setProcessingIds] = useState([]);
    const { keycloak } = useKeycloak();

    // Fetch all pending student registrations
    const fetchPendingRegistrations = async () => {
        try {
            setLoading(true);
            const response = await axios.get(
                'http://localhost:8084/api/registration/etudiant/pending',
                {
                    headers: {
                        Authorization: `Bearer ${keycloak.token}`,
                    },
                }
            );
            setPendingStudents(response.data);
        } catch (error) {
            console.error('Error fetching pending registrations:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to load pending student registrations',
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPendingRegistrations();
    }, [keycloak.token]);

    // Handle approve student registration
    const handleApprove = async (id) => {
        try {
            setProcessingIds((prev) => [...prev, id]);
            
            await axios.post(
                `http://localhost:8084/api/registration/etudiant/${id}/approve`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${keycloak.token}`,
                    },
                }
            );
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Student registration approved successfully!',
            });
            
            // Refresh the list
            fetchPendingRegistrations();
        } catch (error) {
            console.error('Error approving registration:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error.response?.data?.message || 'Failed to approve registration',
            });
        } finally {
            setProcessingIds((prev) => prev.filter(item => item !== id));
        }
    };

    // Handle reject student registration
    const handleReject = async (id) => {
        // Ask for confirmation before rejecting
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: "You are about to reject this student's registration. This action cannot be undone.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, reject it!',
            cancelButtonText: 'Cancel'
        });

        if (result.isConfirmed) {
            try {
                setProcessingIds((prev) => [...prev, id]);
                
                await axios.post(
                    `http://localhost:8084/api/registration/etudiant/${id}/reject`,
                    {},
                    {
                        headers: {
                            Authorization: `Bearer ${keycloak.token}`,
                        },
                    }
                );
                
                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Rejected',
                    text: 'Student registration was rejected',
                });
                
                // Refresh the list
                fetchPendingRegistrations();
            } catch (error) {
                console.error('Error rejecting registration:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: error.response?.data?.message || 'Failed to reject registration',
                });
            } finally {
                setProcessingIds((prev) => prev.filter(item => item !== id));
            }
        }
    };

    // Format the date for display
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    };    return (
        <>
            <div className="page-header">
                <div className="row">
                    <div className="col-sm-12">
                        <h4 className="page-title">Pending Student Registrations</h4>
                        <nav aria-label="breadcrumb">
                            <ol className="breadcrumb">
                                <li className="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
                                <li className="breadcrumb-item">Student Management</li>
                                <li className="breadcrumb-item active" aria-current="page">Pending Registrations</li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>

            <Row>
                <Col lg={12}>
                    <Card>
                        <Card.Header>
                            <Card.Title>Pending Student Registrations</Card.Title>
                        </Card.Header>
                        <Card.Body>
                            {loading ? (
                                <div className="text-center my-4">
                                    <Spinner animation="border" variant="primary" />
                                    <p className="mt-2">Loading pending registrations...</p>
                                </div>
                            ) : pendingStudents.length === 0 ? (
                                <div className="text-center my-4">
                                    <i className="fa fa-check-circle fa-3x text-success mb-3"></i>
                                    <h4>No pending registrations</h4>
                                    <p className="text-muted">
                                        All student registrations have been processed.
                                    </p>
                                </div>
                            ) : (
                                <Table responsive striped>
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Date of Birth</th>
                                            <th>Education Level</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {pendingStudents.map((student) => (
                                            <tr key={student.id}>
                                                <td>{student.id}</td>
                                                <td>
                                                    {student.firstName} {student.lastName}
                                                </td>
                                                <td>{student.email}</td>
                                                <td>{student.phoneNumber || 'N/A'}</td>
                                                <td>{formatDate(student.dateNaissance)}</td>
                                                <td>
                                                    {student.niveau ? student.niveau.nom : 'N/A'}
                                                </td>
                                                <td>
                                                    <Badge bg="warning" pill>
                                                        {student.status}
                                                    </Badge>
                                                </td>
                                                <td>
                                                    <div className="d-flex">
                                                        <Button
                                                            variant="success"
                                                            size="sm"
                                                            className="me-2"
                                                            onClick={() => handleApprove(student.id)}
                                                            disabled={processingIds.includes(student.id)}
                                                        >
                                                            {processingIds.includes(student.id) ? (
                                                                <><Spinner animation="border" size="sm" /> Approving</>
                                                            ) : (
                                                                <>Approve</>
                                                            )}
                                                        </Button>
                                                        <Button
                                                            variant="danger"
                                                            size="sm"
                                                            onClick={() => handleReject(student.id)}
                                                            disabled={processingIds.includes(student.id)}
                                                        >
                                                            {processingIds.includes(student.id) ? (
                                                                <><Spinner animation="border" size="sm" /> Rejecting</>
                                                            ) : (
                                                                <>Reject</>
                                                            )}
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </Table>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </>
    );
};

export default PendingRegistrations;
