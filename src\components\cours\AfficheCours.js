import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Modal, Form, Pagination , Button } from "react-bootstrap";
import axiosInstance from "../../services/axiosService";

const AfficheCours = () => {
  const navigate = useNavigate();
  const [cours, setCours] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedCours, setSelectedCours] = useState(null);
  const [chapitres, setChapitres] = useState([]); // Nouveau state pour les chapitres
  const [formData, setFormData] = useState({
    titre: "",
    description: "",
    lien: "",
    dateCreation: "",
    duree: "",
    pdf: "",
    chapitreId: "", // Nouveau champ pour le chapitre
  });
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState("");
  const itemsPerPage = 8;
const [showDeleteModal, setShowDeleteModal] = useState(false);

  useEffect(() => {
    // Charger les chapitres au chargement du composant
    const fetchChapitres = async () => {
      try {
        const response = await axiosInstance.get("/api/chapitres/all");
        setChapitres(response.data);
      } catch (error) {
        console.error("Erreur lors du chargement des chapitres:", error);
      }
    };
    fetchChapitres();
  }, []);

  useEffect(() => {
    const fetchCours = async () => {
      try {
        let url;
        if (searchTerm) {
          url = `/api/cours/search?titre=${searchTerm}`;
          setIsSearching(true);
        } else {
          url = `/api/cours/page?page=${currentPage}&size=${itemsPerPage}`;
          setIsSearching(false);
        }

        const response = await axiosInstance.get(url);
        const data = response.data;

        if (searchTerm) {
          setCours(data);
          setTotalPages(1);
          setTotalElements(data.length);
        } else {
          setCours(data.content);
          setTotalPages(data.totalPages);
          setTotalElements(data.totalElements);
        }
      } catch (error) {
        console.error("Error fetching cours:", error);
        setError(
          error.response?.data?.message || "Erreur lors du chargement des cours"
        );
      }
    };

    const timer = setTimeout(() => {
      fetchCours();
    }, 300);

    return () => clearTimeout(timer);
  }, [currentPage, searchTerm]);

  const handleShowModal = (cours) => {
    setSelectedCours(cours);
    setFormData({
      titre: cours.titre || "",
      description: cours.description || "",
      lien: cours.lien || "",
      dateCreation: cours.dateCreation ? cours.dateCreation.split("T")[0] : "",
      duree: cours.duree || "",
      pdf: cours.pdf || "",
      chapitreId: cours.chapitre ? cours.chapitre.id : "", // Utilisation de id au lieu de idChapitre
    });
    setShowModal(true);
  };
  const openDeleteModal = (cours) => {
    setSelectedCours(cours);
    setShowDeleteModal(true);
  };

  const handleEdit = async () => {
    if (!selectedCours) return;

    try {
      const updatedCours = {
        idCours: selectedCours.idCours,
        titre: formData.titre,
        description: formData.description,
        lien: formData.lien || null,
        dateCreation: formData.dateCreation || new Date().toISOString(),
        duree: formData.duree ? parseInt(formData.duree, 10) : 0,
        pdf: formData.pdf || null,
        chapitre: {
          id: parseInt(formData.chapitreId),
        },
      };

      const response = await axiosInstance.put(
        `/api/cours/${selectedCours.idCours}`,
        updatedCours
      );

      setCours((prev) =>
        prev.map((cours) =>
          cours.idCours === selectedCours.idCours
            ? {
                ...response.data,
                chapitre: chapitres.find(
                  (c) => c.id === parseInt(formData.chapitreId)
                ),
              }
            : cours
        )
      );
      setShowModal(false);
      setError("");
    } catch (error) {
      console.error("Error updating cours:", error);
      setError(
        error.response?.data?.message ||
          "Erreur lors de la mise à jour du cours"
      );
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const deleteCours = async (id) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce cours ?")) {
      try {
        await axiosInstance.delete(`/api/cours/${id}`);
        setCours((prev) => prev.filter((cours) => cours.idCours !== id));
        setError("");
      } catch (error) {
        console.error("Error deleting cours:", error);
        setError(
          error.response?.data?.message ||
            "Erreur lors de la suppression du cours"
        );
      }
    }
  };
  // ✅ Fonction pour aller à la page suivante
const goToNextPage = () => {
  if (currentPage < totalPages) {
    setCurrentPage(currentPage + 1);
  }
};

// ✅ Fonction pour revenir à la page précédente
const goToPrevPage = () => {
  if (currentPage > 1) {
    setCurrentPage(currentPage - 1);
  }
};

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedCours(null);
    setError("");
  };

  const formatDate = (date) => {
    if (!date) return "";
    const d = new Date(date);
    return d.toLocaleDateString("fr-FR");
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const renderPagination = () => {
    let items = [];
    for (let number = 0; number < totalPages; number++) {
      items.push(
        <Pagination.Item
          key={number}
          active={number === currentPage}
          onClick={() => handlePageChange(number)}
        >
          {number + 1}
        </Pagination.Item>
      );
    }

    return (
      <div className="d-flex justify-content-center mt-4">
        <Pagination>
          <Pagination.First
            onClick={() => handlePageChange(0)}
            disabled={currentPage === 0}
          />
          <Pagination.Prev
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 0}
          />
          {items}
          <Pagination.Next
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages - 1}
          />
          <Pagination.Last
            onClick={() => handlePageChange(totalPages - 1)}
            disabled={currentPage === totalPages - 1}
          />
        </Pagination>
      </div>
    );
  };

  const renderVideoLink = (lien) => {
    if (!lien) return null;

    // Gestion du format stocké dans la base de données (/videos/ID)
    const dbFormatMatch = lien.match(/\/videos\/(\d+)/);
    if (dbFormatMatch && dbFormatMatch[1]) {
      const videoId = dbFormatMatch[1];
      const embedUrl = `https://player.vimeo.com/video/${videoId}`;
      return (
        <div className="embed-responsive embed-responsive-16by9 mb-3">
          <iframe
            className="embed-responsive-item"
            src={embedUrl}
            width="100%"
            height="200"
            frameBorder="0"
            allow="autoplay; fullscreen; picture-in-picture"
            allowFullScreen
            title="Vidéo du cours"
          ></iframe>
        </div>
      );
    }

    // Si c'est un lien complet Vimeo
    const vimeoMatch = lien.match(
      /(?:vimeo\.com\/|player\.vimeo\.com\/video\/|)(\d+)/
    );
    if (vimeoMatch && vimeoMatch[1]) {
      const videoId = vimeoMatch[1];
      const embedUrl = `https://player.vimeo.com/video/${videoId}`;
      return (
        <div className="embed-responsive embed-responsive-16by9 mb-3">
          <iframe
            className="embed-responsive-item"
            src={embedUrl}
            width="100%"
            height="200"
            frameBorder="0"
            allow="autoplay; fullscreen; picture-in-picture"
            allowFullScreen
            title="Vidéo du cours"
          ></iframe>
        </div>
      );
    }

    // Si le lien n'est pas reconnu
    return (
      <a
        href={`https://vimeo.com${lien}`}
        target="_blank"
        rel="noopener noreferrer"
        className="btn btn-primary btn-sm"
      >
        Voir la vidéo
      </a>
    );
  };

  return (
    <div
      className="container-fluid"
      style={{ backgroundColor: "#F6F4EE", minHeight: "100vh" }}
    >
      <div className="row page-titles mx-0 d-flex align-items-center justify-content-between flex-wrap">
        <div className="col-auto">
          <h4 className="fw-bold" style={{ color: "#000080" }}>
            Tous Les cours
          </h4>
          <p>Total: {isSearching ? cours.length : totalElements} cours</p>
        </div>

        <div className="col-md-4">
          <Form.Control
            type="text"
            placeholder="🔍Rechercher un cours par titre..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="shadow-sm rounded-pill px-3"
            style={{
              backgroundColor: "#EEF9F5",
              borderColor: "#B7B7B7",
              color: "#1D1D1B",
            }}
          />
        </div>
        <div className="col-auto">
          <button
            className="btn rounded-pill shadow-sm px-4"
            onClick={() => navigate("/cours/ajouter")}
            style={{
              backgroundColor: "#37A7DF",
              borderColor: "#37A7DF",
              color: "#fff",
            }}
          >
            + Ajouter un cours
          </button>
        </div>
      </div>

      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      <div className="row">
        {cours.map((cours) => (
          <div
            key={cours.idCours}
            className="col-xl-3 col-xxl-4 col-lg-4 col-md-6 col-sm-6"
          >
            <div className=" card shadow-sm h-100 border-0 hover-shadow">
              <div className="card-body">
              <h5 className="card-title" style={{ color: "#37A7DF" }}>
              {cours.titre}                    </h5>

                {cours.lien && renderVideoLink(cours.lien)}
                <ul className="list-group mb-3 list-group-flush">
                  <li className="list-group-item px-0 border-top-0 d-flex justify-content-between">
                    <span className="mb-0" style={{ color: "#1D1D1B" }}>Description : </span>
                    <span>{cours.description}</span>
                  </li>
                  <li className="list-group-item px-0 d-flex justify-content-between">
                    <span className="mb-0" style={{ color: "#1D1D1B" }}>Durée :</span>
                    <strong style={{ color: "#1D1D1B" }}>{cours.duree} minutes</strong>
                  </li>
                  <li className="list-group-item px-0 d-flex justify-content-between">
                    <span className="mb-0" style={{ color: "#1D1D1B" }}>Date de création :</span>
                    <strong>{formatDate(cours.dateCreation)}</strong>
                  </li>
                  <li className="list-group-item px-0 d-flex justify-content-between">
                    <span className="mb-0" style={{ color: "#1D1D1B" }}>Chapitre :</span>
                    <strong style={{ color: "#1D1D1B" }}>
                      {cours.chapitre
                        ? cours.chapitre.nomChapitre
                        : "Non défini"}
                    </strong>
                  </li>
                  {cours.pdf && (
                    <li className="list-group-item px-0 d-flex justify-content-between">
                      <span className="mb-0">PDF :</span>
                      <a
                        href={cours.pdf}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="btn btn-info btn-sm"
                      >
                        Voir le PDF
                      </a>
                    </li>
                  )}
                </ul>
                <div  className="position-absolute"
                      style={{
                        bottom: "15px",
                        right: "15px",
                        display: "flex",
                        gap: "10px",
                      }}>
                  <button
                    onClick={() => handleShowModal(cours)}
                    className="btn"
                    style={{
                      backgroundColor: "#F2BC00",
                      color: "#1D1D1B",
                      borderRadius: "12px",
                      width: "40px",
                      height: "40px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                    }}
                    title="Modifier"

                  >
                        <i className="fa fa-edit"></i>
                        </button>
                  <button
                    onClick={() => openDeleteModal(cours.idCours)}
                    className="btn"
                    style={{
                      backgroundColor: "#D9534F",
                      color: "#fff",
                      borderRadius: "12px",
                      width: "40px",
                      height: "40px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                    }}    
                    title="Supprimer"
                    >
                        <i className="fa fa-trash"></i>
                        </button>
                </div>
              </div>
            </div>
          </div>
        ))}
         <div className="d-flex justify-content-center mt-4">
            <button
              className="btn mx-2"
              style={{
                backgroundColor: "#37A7DF",
                color: "white",
                borderRadius: "20px",
                padding: "6px 18px",
                border: "none",
              }}
              onClick={goToPrevPage}
              disabled={currentPage === 1}
            >
              <i className="fa fa-arrow-left me-1" /> Précédent
            </button>
            <span className="align-self-center text-dark fw-semibold">
              Page <span style={{ color: "#F2BC00" }}>{currentPage}</span> sur{" "}
              <span>{totalPages}</span>
            </span>
            <button
              className="btn mx-2"
              style={{
                backgroundColor: "#37A7DF",
                color: "white",
                borderRadius: "20px",
                padding: "6px 18px",
                border: "none",
              }}
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
            >
              Suivant <i className="fa fa-arrow-right ms-1" />
            </button>
          </div>
        </div>

      {/*{!isSearching && renderPagination()}*/}
      

      <Modal show={showModal} onHide={handleCloseModal}>
        <Modal.Header closeButton>
          <Modal.Title>Modifier le cours</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Titre</Form.Label>
              <Form.Control
                type="text"
                name="titre"
                value={formData.titre}
                onChange={handleChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={formData.description}
                onChange={handleChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Lien de la vidéo</Form.Label>
              <Form.Control
                type="text"
                name="lien"
                value={formData.lien}
                onChange={handleChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Lien du PDF</Form.Label>
              <Form.Control
                type="text"
                name="pdf"
                value={formData.pdf}
                onChange={handleChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Date de création</Form.Label>
              <Form.Control
                type="date"
                name="dateCreation"
                value={formData.dateCreation}
                onChange={handleChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Durée (minutes)</Form.Label>
              <Form.Control
                type="number"
                name="duree"
                value={formData.duree}
                onChange={handleChange}
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Chapitre</Form.Label>
              <Form.Select
                name="chapitreId"
                value={formData.chapitreId}
                onChange={handleChange}
              >
                <option value="">Sélectionner un chapitre</option>
                {chapitres.map((chapitre) => (
                  <option key={chapitre.id} value={chapitre.id}>
                    {chapitre.nomChapitre}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <button className="btn btn-secondary" onClick={handleCloseModal}>
            Annuler
          </button>
          <button
            className="btn btn-primary"
            onClick={handleEdit}
            style={{
              backgroundColor: "#37A7DF",
              borderColor: "#37A7DF",
            }}
          >
            Enregistrer
          </button>
        </Modal.Footer>
      </Modal>
       {/* Modale de suppression */}
              <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
                <Modal.Header closeButton style={{ backgroundColor: "#FCEBEA" }}>
                  <Modal.Title style={{ color: "#D9534F" }}>
                    <i className="fa fa-exclamation-triangle me-2" /> Confirmer la
                    suppression
                  </Modal.Title>
                </Modal.Header>
                <Modal.Body style={{ color: "#1D1D1B" }}>
                  Êtes-vous sûr de vouloir supprimer{" "}
                  <strong>"{selectedCours?.nom}"</strong> ?
                </Modal.Body>
                <Modal.Footer>
                  <Button
                    variant="secondary"
                    onClick={() => setShowDeleteModal(false)}
                    style={{ backgroundColor: "#6c757d", borderColor: "#6c757d" }}
                  >
                    Annuler
                  </Button>
                  <Button
                    variant="danger"
                    onClick={deleteCours}
                    style={{ backgroundColor: "#D9534F", borderColor: "#D9534F" }}
                  >
                    Supprimer
                  </Button>
                </Modal.Footer>
              </Modal>
    </div>
  );
};

export default AfficheCours;
