import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import keycloak from '../keycloak';
import '../styles/responsive.css';
import '../styles/theme.css';

const Sidebar = ({ className, isOpen, toggleSidebar }) => {
  const [expandedMenus, setExpandedMenus] = useState({
    profs: false,
    etudiants: false,
    niveaux: false,
    matieres: false,
    chapitres: false,
    cours: false,
    abonnements: false,
    typeAbonnements: false,
    liveSessions: false,
    liveSessionsStudent: false,
    recordings: false,
    profile: false,
    aiTools: false
  });

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const toggleMenu = (menu) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };

  // Close sidebar when clicking on a link on mobile
  const handleLinkClick = () => {
    if (windowWidth < 992 && isOpen) {
      toggleSidebar();
    }
  };

  return (
    <>
      {/* Overlay for mobile */}
      <div className={`overlay ${isOpen ? 'show' : ''}`} onClick={toggleSidebar}></div>

      {/* Sidebar */}
      <div className={`dlabnav ${className} ${isOpen ? 'show-menu' : ''}`}
           style={{
             padding: '15px 0',
             backgroundColor: 'var(--sidebar-bg)',
             color: 'var(--text-light)'
           }}>
        <div className="dlabnav-scroll">
          <ul className="metismenu" id="menu" style={{ listStyle: 'none', paddingLeft: '15px' }}>
          {/* Titre MENU PRINCIPAL */}
          <li className="nav-label first" style={{
           fontWeight: 'bold',
           fontSize: '1.1rem',
           margin: '10px 0 20px 0',
           color: 'var(--primary-yellow)',
           letterSpacing: '1px',
           textTransform: 'uppercase'
          }}>
            MENU PRINCIPAL
          </li>

          {/* Tableau de bord */}
          <li style={{ marginBottom: '12px' }}>
            <Link
              to="/dashboard"
              className="has-arrow"
              onClick={handleLinkClick}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                textDecoration: 'none',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease'
              }}
            >
             <i className="la la-home" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem'
              }}>
                Tableau de bord
              </span>
            </Link>
          </li>

          {/* Profil - Accessible pour les étudiants et les enseignants */}
          {(keycloak.hasRealmRole('ETUDIANT') || keycloak.hasRealmRole('ENSEIGNANT')) && (
          <li style={{ marginBottom: '12px' }}>
            <Link
              to="/profile"
              className="has-arrow"
              onClick={handleLinkClick}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                textDecoration: 'none',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease'
              }}
            >
             <i className="la la-user-circle" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem'
              }}>
                Mon Profil
              </span>
            </Link>
          </li>)}

          {/* Professeurs */}
          {keycloak.hasRealmRole('ADMIN') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.profs ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('profs')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease',
                backgroundColor: expandedMenus.profs ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-user" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.profs ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.profs ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Professeurs
              </span>
              <i className={`la la-angle-down ${expandedMenus.profs ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.profs ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>

            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.profs ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>


{keycloak.hasRealmRole('ADMIN') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/enseignants"
                  onClick={handleLinkClick}
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: 'var(--primary-dark)',
                    fontWeight: '500',
                    borderRadius: '4px',
                    transition: 'all 0.3s ease'
                  }}
                  className="sidebar-link"
                >
                  Tous les professeurs
                </Link>
              </li>)}
              {keycloak.hasRealmRole('ADMIN') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/ajouter-enseignant"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Ajouter un professeur
                </Link>
              </li>)}
            </ul>
          </li>)}

          {/* Étudiants */}
          {keycloak.hasRealmRole('ADMIN') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.etudiants ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('etudiants')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease',
                backgroundColor: expandedMenus.etudiants ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-users" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.etudiants ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.etudiants ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Étudiants
              </span>
              <i className={`la la-angle-down ${expandedMenus.etudiants ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.etudiants ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.etudiants ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/afficher-etudiant"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: 'var(--primary-dark)',
                    fontWeight: '500',
                    borderRadius: '4px',
                    transition: 'all 0.3s ease'
                  }}
                  className="sidebar-link"
                >
                  Tous les étudiants
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/ajouter-etudiant"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Ajouter un étudiant
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/pending-registrations"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500',
                    position: 'relative'
                  }}
                >
                  Inscriptions en attente
                  <span style={{
                    position: 'absolute',
                    right: '10px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: '#f8423c',
                    color: 'white',
                    padding: '2px 8px',
                    borderRadius: '10px',
                    fontSize: '10px',
                    fontWeight: 'bold'
                  }}>

                  </span>
                </Link>
              </li>

            </ul>
          </li>)}
          {/* Niveaux d'étude */}
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.niveaux ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('niveaux')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease',
                backgroundColor: expandedMenus.niveaux ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-layer-group" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.niveaux ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.niveaux ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Niveau d'étude
              </span>
              <i className={`la la-angle-down ${expandedMenus.niveaux ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.niveaux ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.niveaux ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>
              {keycloak.hasRealmRole('ADMIN') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/niveaux-etude"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Tous les niveaux d'étude
                </Link>
              </li>)}
              {keycloak.hasRealmRole('ADMIN') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/niveaux-etude/ajouter"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Ajouter niveau
                </Link>
              </li>)}
              {keycloak.hasRealmRole('ENSEIGNANT') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/mes-niveaux"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Mes niveaux
                </Link>
              </li>)}


            </ul>
          </li>
          {/* matieres */}
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.matieres ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('matieres')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease',
                backgroundColor: expandedMenus.matieres ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-book" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.matieres ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.matieres ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Matières
              </span>
              <i className={`la la-angle-down ${expandedMenus.matieres ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.matieres ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.matieres ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>
              {keycloak.hasRealmRole('ADMIN') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/matieres"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                 Toutes les matières
                </Link>
              </li>)}
              {keycloak.hasRealmRole('ENSEIGNANT') && (
                <li style={{ marginBottom: '8px' }}>
                  <Link
                    to="/mes-matieres"
                    style={{
                      display: 'block',
                      padding: '8px 15px',
                      textDecoration: 'none',
                      color: '#555',
                      fontWeight: '500'
                    }}
                  >
                    Mes matières
                  </Link>
                </li>
              )}
               {keycloak.hasRealmRole('ETUDIANT') && (
                <li style={{ marginBottom: '8px' }}>
                  <Link
                    to="/Mes-matieresETUD"
                    style={{
                      display: 'block',
                      padding: '8px 15px',
                      textDecoration: 'none',
                      color: '#555',
                      fontWeight: '500'
                    }}
                  >
                    Mes matières
                  </Link>
                </li>
              )}

              {keycloak.hasRealmRole('ADMIN') && (
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/matieres/ajouter"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Ajouter une matière
                </Link>
              </li>)}

            </ul>
          </li>
          {/* Professeurs */}
          {keycloak.hasRealmRole('ADMIN') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.chapitres ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('chapitres')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: '#333',
                borderRadius: '4px'
              }}
            >
              <i className="la la-user" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center'
              }}></i>
              <span style={{
                fontWeight: 'bold',
                fontSize: '1rem',
                flex: 1
              }}>
                Chapitres
              </span>
              <i className={`la la-angle-down ${expandedMenus.chapitres ? 'rotate-180' : ''}`}
                 style={{ transition: 'transform 0.3s' }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.chapitres ? 'block' : 'none'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/chapitres"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Tous les chapitres
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/chapitres/ajouter"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Ajouter chapitre
                </Link>
              </li>

            </ul>
          </li>)}
             {/* Cours */}

             {keycloak.hasRealmRole('ADMIN') && (
             <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.cours ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('cours')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: '#333',
                borderRadius: '4px'
              }}
            >
              <i className="la la-user" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center'
              }}></i>
              <span style={{
                fontWeight: 'bold',
                fontSize: '1rem',
                flex: 1
              }}>
                Cours
              </span>
              <i className={`la la-angle-down ${expandedMenus.cours ? 'rotate-180' : ''}`}
                 style={{ transition: 'transform 0.3s' }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.cours ? 'block' : 'none'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/cours"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Tous les cours
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/AjouterCours"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Ajouter un cours
                </Link>
              </li>

            </ul>
          </li>)}
          {/* Live Sessions - Only for Teachers */}
          {keycloak.hasRealmRole('ENSEIGNANT') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.liveSessions ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('liveSessions')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: '#333',
                borderRadius: '4px'
              }}
            >
              <i className="la la-video" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center'
              }}></i>
              <span style={{
                fontWeight: 'bold',
                fontSize: '1rem',
                flex: 1
              }}>
                Sessions en direct
              </span>
              <i className={`la la-angle-down ${expandedMenus.liveSessions ? 'rotate-180' : ''}`}
                 style={{ transition: 'transform 0.3s' }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.liveSessions ? 'block' : 'none'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/live-sessions"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Toutes les sessions
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/live-sessions/create"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Créer une session
                </Link>
              </li>
            </ul>
          </li>)}
          {keycloak.hasRealmRole('ADMIN') && (
  <li style={{ marginBottom: '12px' }}>
    <div
      className={`has-arrow ${expandedMenus.typeAbonnements ? 'mm-active' : ''}`}
      onClick={() => toggleMenu('typeAbonnements')}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '10px 15px',
        cursor: 'pointer',
        color: '#333',
        borderRadius: '4px',
        backgroundColor: expandedMenus.typeAbonnements ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
      }}
    >
      <i className="la la-tags" style={{
        marginRight: '12px',
        fontSize: '1.3rem',
        width: '24px',
        textAlign: 'center',
        color: 'var(--primary-blue)'
      }}></i>
      <span style={{
        fontWeight: 'bold',
        fontSize: '1rem',
        flex: 1
      }}>
        Types d'abonnements
      </span>
      <i className={`la la-angle-down ${expandedMenus.typeAbonnements ? 'rotate-180' : ''}`}
         style={{ transition: 'transform 0.3s' }}></i>
    </div>

    <ul style={{
      paddingLeft: '20px',
      marginTop: '5px',
      listStyle: 'none',
      display: expandedMenus.typeAbonnements ? 'block' : 'none'
    }}>
      <li style={{ marginBottom: '8px' }}>
        <Link
          to="/type-abonnements"
          style={{
            display: 'block',
            padding: '8px 15px',
            textDecoration: 'none',
            color: '#555',
            fontWeight: '500'
          }}
        >
          Tous les types
        </Link>
      </li>
      <li style={{ marginBottom: '8px' }}>
        <Link
          to="/ajouter-type-abonnement"
          style={{
            display: 'block',
            padding: '8px 15px',
            textDecoration: 'none',
            color: '#555',
            fontWeight: '500'
          }}
        >
          Ajouter un type
        </Link>
      </li>
    </ul>
  </li>
)}

{(keycloak.hasRealmRole('ADMIN') || keycloak.hasRealmRole('ETUDIANT')) && (
  <li style={{ marginBottom: '12px' }}>
    <div
      className={`has-arrow ${expandedMenus.abonnements ? 'mm-active' : ''}`}
      onClick={() => toggleMenu('abonnements')}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '10px 15px',
        cursor: 'pointer',
        color: '#333',
        borderRadius: '4px',
        backgroundColor: expandedMenus.abonnements ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
      }}
    >
      <i className="la la-credit-card" style={{
        marginRight: '12px',
        fontSize: '1.3rem',
        width: '24px',
        textAlign: 'center',
        color: 'var(--primary-blue)'
      }}></i>
      <span style={{
        fontWeight: 'bold',
        fontSize: '1rem',
        flex: 1
      }}>
        Abonnements
      </span>
      <i className={`la la-angle-down ${expandedMenus.abonnements ? 'rotate-180' : ''}`}
         style={{ transition: 'transform 0.3s' }}></i>
    </div>

    <ul style={{
      paddingLeft: '20px',
      marginTop: '5px',
      listStyle: 'none',
      display: expandedMenus.abonnements ? 'block' : 'none'
    }}>
      {keycloak.hasRealmRole('ADMIN') && (
        <>
          <li style={{ marginBottom: '8px' }}>
            <Link
              to="/abonnements"
              style={{
                display: 'block',
                padding: '8px 15px',
                textDecoration: 'none',
                color: '#555',
                fontWeight: '500'
              }}
            >
              Tous les abonnements
            </Link>
          </li>
          <li style={{ marginBottom: '8px' }}>
            <Link
              to="/abonnements/ajouter"
              style={{
                display: 'block',
                padding: '8px 15px',
                textDecoration: 'none',
                color: '#555',
                fontWeight: '500'
              }}
            >
              Ajouter abonnement
            </Link>
          </li>
        </>
      )}


      {keycloak.hasRealmRole('ETUDIANT') && (
        <li style={{ marginBottom: '8px' }}>
          <Link
            to="/Mes-abonnement"
            style={{
              display: 'block',
              padding: '8px 15px',
              textDecoration: 'none',
              color: '#555',
              fontWeight: '500'
            }}
          >
            Mes abonnements
          </Link>
        </li>
      )}
    </ul>
  </li>
)}
{keycloak.hasRealmRole('ETUDIANT') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.liveSessionsStudent ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('liveSessionsStudent')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: '#333',
                borderRadius: '4px'
              }}
            >
              <i className="la la-video" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center'
              }}></i>
              <span style={{
                fontWeight: 'bold',
                fontSize: '1rem',
                flex: 1
              }}>
                Sessions en direct
              </span>
              <i className={`la la-angle-down ${expandedMenus.liveSessionsStudent ? 'rotate-180' : ''}`}
                 style={{ transition: 'transform 0.3s' }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.liveSessionsStudent ? 'block' : 'none'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/live-sessions/available"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Sessions disponibles
                </Link>
              </li>

              {/*
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/live-sessions/join"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Rejoindre une session
                </Link>
              </li>*/}
            </ul>
          </li>)}

          {/* Recordings - For Students */}
          {keycloak.hasRealmRole('ETUDIANT') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.recordings ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('recordings')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: '#333',
                borderRadius: '4px',
                backgroundColor: expandedMenus.recordings ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-play-circle" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.recordings ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: 'bold',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.recordings ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Enregistrements
              </span>
              <i className={`la la-angle-down ${expandedMenus.recordings ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.recordings ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.recordings ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/recordings"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Tous les enregistrements
                </Link>
              </li>
            </ul>
          </li>)}

          {/* AI Tools - For Students */}
          {keycloak.hasRealmRole('ETUDIANT') && (
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.aiTools ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('aiTools')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: '#333',
                borderRadius: '4px',
                backgroundColor: expandedMenus.aiTools ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-robot" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.aiTools ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: 'bold',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.aiTools ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Outils IA
              </span>
              <i className={`la la-angle-down ${expandedMenus.aiTools ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.aiTools ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.aiTools ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/text-to-voice"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Texte en Voix
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/chat-model"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Assistant IA
                </Link>
              </li>
            </ul>
          </li>)}

          {/* Forum de discussion - Pour tous les utilisateurs */}
          <li style={{ marginBottom: '12px' }}>
            <div
              className={`has-arrow ${expandedMenus.forum ? 'mm-active' : ''}`}
              onClick={() => toggleMenu('forum')}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 15px',
                cursor: 'pointer',
                color: 'var(--primary-dark)',
                borderRadius: '4px',
                transition: 'all 0.3s ease',
                backgroundColor: expandedMenus.forum ? 'rgba(242, 188, 0, 0.1)' : 'transparent'
              }}
            >
              <i className="la la-comments" style={{
                marginRight: '12px',
                fontSize: '1.3rem',
                width: '24px',
                textAlign: 'center',
                color: expandedMenus.forum ? 'var(--primary-yellow)' : 'var(--primary-blue)'
              }}></i>
              <span style={{
                fontWeight: '600',
                fontSize: '1rem',
                flex: 1,
                color: expandedMenus.forum ? 'var(--primary-yellow)' : 'var(--primary-dark)'
              }}>
                Forum de discussion
              </span>
              <i className={`la la-angle-down ${expandedMenus.forum ? 'rotate-180' : ''}`}
                 style={{
                   transition: 'transform 0.3s',
                   color: expandedMenus.forum ? 'var(--primary-yellow)' : 'var(--primary-dark)'
                 }}></i>
            </div>
            <ul style={{
              paddingLeft: '20px',
              marginTop: '5px',
              listStyle: 'none',
              display: expandedMenus.forum ? 'block' : 'none',
              transition: 'all 0.3s ease'
            }}>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/forum"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Toutes les questions
                </Link>
              </li>
              <li style={{ marginBottom: '8px' }}>
                <Link
                  to="/forum/poser-question"
                  style={{
                    display: 'block',
                    padding: '8px 15px',
                    textDecoration: 'none',
                    color: '#555',
                    fontWeight: '500'
                  }}
                >
                  Poser une question
                </Link>
              </li>
              {keycloak.hasRealmRole('ETUDIANT') && (
                <li style={{ marginBottom: '8px' }}>
                  <Link
                    to="/forum/mes-questions"
                    style={{
                      display: 'block',
                      padding: '8px 15px',
                      textDecoration: 'none',
                      color: '#555',
                      fontWeight: '500'
                    }}
                  >
                    Mes questions
                  </Link>
                </li>
              )}
            </ul>
          </li>

          {/* Répétez le même pattern pour les autres sections */}

          {/* Acheter Abonnement */}

        </ul>
      </div>

      {/* CSS intégré */}
      <style>{`
        .rotate-180 {
          transform: rotate(180deg);
        }
        .has-arrow:hover {
          background-color: rgba(55, 167, 223, 0.1);
        }
        .metismenu li a.active {
          color: var(--primary-blue) !important;
          font-weight: 600 !important;
        }
      `}</style>
    </div>
    </>
  );
};

export default Sidebar;