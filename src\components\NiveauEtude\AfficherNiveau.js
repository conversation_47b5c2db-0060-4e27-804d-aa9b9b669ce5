import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Mo<PERSON>, Button, Form } from "react-bootstrap";
import axiosInstance from "../../services/axiosService";

const AfficherNiveau = () => {
  const navigate = useNavigate();
  const [niveaux, setNiveaux] = useState([]);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedNiveau, setSelectedNiveau] = useState(null);
  const [editedNom, setEditedNom] = useState("");

  useEffect(() => {
    axiosInstance.get('/api/niveaux/all')
      .then((response) => {
        setNiveaux(response.data);
      })
      .catch((error) => {
        console.error('Error fetching niveaux:', error);
        setError(error.response?.data?.message || "Impossible de récupérer les niveaux d'études");
      });
  }, []);

  const handleDelete = () => {
    axiosInstance.delete(`/api/niveaux/${selectedNiveau.id}`)
      .then(() => {
        setNiveaux((prev) => prev.filter((n) => n.id !== selectedNiveau.id));
        setShowDeleteModal(false);
      })
      .catch((error) => {
        console.error("Erreur lors de la suppression:", error);
        setError(error.response?.data?.message || "Erreur lors de la suppression du niveau");
      });
  };

  const handleEdit = () => {
    axiosInstance.put(`/api/niveaux/${selectedNiveau.id}`, { nom: editedNom })
      .then((response) => {
        setNiveaux((prev) =>
          prev.map((n) => (n.id === selectedNiveau.id ? response.data : n))
        );
        setShowEditModal(false);
      })
      .catch((error) => {
        console.error("Erreur:", error);
        setError(error.response?.data?.message || "Impossible de mettre à jour le niveau.");
      });
  };
  
  const openEditModal = (niveau) => {
    setSelectedNiveau(niveau);
    setEditedNom(niveau.nom);
    setShowEditModal(true);
  };

  const openDeleteModal = (niveau) => {
    setSelectedNiveau(niveau);
    setShowDeleteModal(true);
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0"style={{ backgroundColor: "#EEF9F5", borderColor: "#B7B7B7", boxShadow: "0 2px 10px rgba(0,0,0,0.1)" }}>
        <div className="col-sm-6 p-md-0">
          <h4 style={{ color: "#37A7DF" }} >Niveaux d'Études</h4>
        </div>
        <div className="col-sm-6 d-flex justify-content-end">
          <button
            className="btn btn-primary"
            style={{
              backgroundColor: "#37A7DF",
              borderColor: "#37A7DF",
              color: "#fff",
            }}
            onClick={() => navigate("/niveaux-etude/ajouter")}
          >
            + Ajouter un Niveau
          </button>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div className="row">
        {niveaux.map((niveau) => (
          <div key={niveau.id} className="col-xl-3 col-lg-4 col-md-6">
            <div className="card" style={{ backgroundColor: "#EEF9F5", borderColor: "#B7B7B7", boxShadow: "0 2px 10px rgba(0,0,0,0.1)" }}>
              <div className="card-body" style={{ color: "#1D1D1B" }}>
                <h4>{niveau.nom}</h4>
                <div
                  className="position-absolute"
                  style={{
                    bottom: "15px",
                    right: "15px",
                    display: "flex",
                    gap: "10px",
                  }}
                >
                  <button
                    className="btn"
                    style={{
                      backgroundColor: "#F2BC00",
                      color: "#1D1D1B",
                      borderRadius: "12px",
                      width: "40px",
                      height: "40px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                    }}
                    onClick={() => openEditModal(niveau)}
                    title="Modifier"
                  >
                    <i className="fa fa-edit"></i>
                  </button>
                  <button
                    className="btn"
                    style={{
                      backgroundColor: "#D9534F",
                      color: "#fff",
                      borderRadius: "12px",
                      width: "40px",
                      height: "40px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                    }}
                    onClick={() => openDeleteModal(niveau)}
                    title="Supprimer"
                  >
                    <i className="fa fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal de suppression */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirmer la suppression</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Êtes-vous sûr de vouloir supprimer ce niveau d'étude ?
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Annuler
          </Button>
          <Button variant="danger" onClick={handleDelete}>
            Supprimer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Modal de modification */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Modifier le niveau</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Nom du niveau</Form.Label>
              <Form.Control
                type="text"
                value={editedNom}
                onChange={(e) => setEditedNom(e.target.value)}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowEditModal(false)}>
            Annuler
          </Button>
          <Button
            variant="primary"
            onClick={handleEdit}
            style={{
              backgroundColor: "#37A7DF",
              borderColor: "#37A7DF",
            }}
          >
            Enregistrer
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default AfficherNiveau;
