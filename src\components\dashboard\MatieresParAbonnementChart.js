import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  LabelList,
} from "recharts";

const MatieresParAbonnementChart = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Try the statistics endpoint first
        let res;
        try {
          res = await axiosInstance.get("/api/statistics/matieres-par-abonnement");
          console.log("API Response from statistics:", res.data);
        } catch (statsError) {
          console.log("Statistics endpoint failed, trying alternative approach...");
          console.log("Stats error details:", statsError.response?.status, statsError.response?.data);

          // Alternative: Get all abonnements and count their matieres
          try {
            const abonnementsRes = await axiosInstance.get("/api/abonnements/all");
            const abonnements = abonnementsRes.data;
            console.log("Abonnements data:", abonnements);

            // If abonnements is paginated, handle it
            const abonnementsList = Array.isArray(abonnements) ? abonnements :
                                   (abonnements.content ? abonnements.content : []);

            if (abonnementsList.length === 0) {
              console.log("No abonnements found, trying to get matieres directly...");

              // Try to get matieres and group by abonnement
              try {
                const matieresRes = await axiosInstance.get("/api/matieres/all");
                const matieres = Array.isArray(matieresRes.data) ? matieresRes.data :
                               (matieresRes.data.content ? matieresRes.data.content : []);

                console.log("Matieres data:", matieres);

                // Group matieres by abonnement
                const abonnementGroups = {};
                matieres.forEach(matiere => {
                  if (matiere.abonnementIds && Array.isArray(matiere.abonnementIds)) {
                    matiere.abonnementIds.forEach(abonnement => {
                      const abonnementName = abonnement.nom || abonnement.name || `Abonnement ${abonnement.id}`;
                      if (!abonnementGroups[abonnementName]) {
                        abonnementGroups[abonnementName] = 0;
                      }
                      abonnementGroups[abonnementName]++;
                    });
                  }
                });

                const matieresCount = Object.entries(abonnementGroups).map(([nom, count]) => ({
                  abonnement: nom,
                  nombreMatieres: count
                }));

                res = { data: matieresCount };
                console.log("Matieres-based data created:", matieresCount);
              } catch (matieresError) {
                console.error("Matieres approach failed:", matieresError);
                // Create sample data for demonstration
                res = { data: [
                  { abonnement: "Basic", nombreMatieres: 0 },
                  { abonnement: "Standard", nombreMatieres: 0 },
                  { abonnement: "Premium", nombreMatieres: 0 }
                ]};
                console.log("Using sample data");
              }
            } else {
              // Count matieres for each abonnement
              const matieresCount = abonnementsList.map(abonnement => {
                let nombreMatieres = 0;

                // Try different possible property names for matieres
                if (abonnement.matieres && Array.isArray(abonnement.matieres)) {
                  nombreMatieres = abonnement.matieres.length;
                } else if (abonnement.matieresIds && Array.isArray(abonnement.matieresIds)) {
                  nombreMatieres = abonnement.matieresIds.length;
                } else if (abonnement.nombreMatieres) {
                  nombreMatieres = abonnement.nombreMatieres;
                }

                return {
                  abonnement: abonnement.nom || abonnement.name || abonnement.type || `Abonnement ${abonnement.id}`,
                  nombreMatieres: nombreMatieres
                };
              });

              res = { data: matieresCount };
              console.log("Alternative data created:", matieresCount);
            }
          } catch (altError) {
            console.error("Alternative approach also failed:", altError);
            throw new Error("Impossible de récupérer les données des matières par abonnement");
          }
        }

        // Handle different possible data structures
        let rawData = res.data;
        if (!Array.isArray(rawData)) {
          if (rawData.content && Array.isArray(rawData.content)) {
            rawData = rawData.content;
          } else if (rawData.data && Array.isArray(rawData.data)) {
            rawData = rawData.data;
          } else {
            console.error("Unexpected data structure:", rawData);
            throw new Error("Structure de données inattendue");
          }
        }

        // Transform data to match expected format
        const formattedData = rawData.map((item, index) => {
          // Handle different possible property names
          const abonnementName = item.abonnement ||
                                 item.abonnementNom ||
                                 item.nom ||
                                 item.name ||
                                 item.typeAbonnement ||
                                 `Abonnement ${index + 1}`;

          const nombreMatieres = item.nombreMatieres ||
                                 item.count ||
                                 item.total ||
                                 item.matieresCount ||
                                 0;

          return {
            abonnement: abonnementName,
            nombreMatieres: parseInt(nombreMatieres) || 0
          };
        });

        console.log("Formatted data:", formattedData);

        // Don't filter out entries with 0 matieres - show all abonnements
        setData(formattedData);
        setError(null);

      } catch (error) {
        console.error("Erreur lors du chargement des données :", error);
        setError(error.message || error.response?.data?.message || "Erreur de chargement des données");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);


  if (loading) {
    return (
      <div className="chart-container" style={{ padding: "20px", textAlign: "center" }}>
        <h5 className="chart-title" style={{ color: "#37A7DF", marginBottom: "20px" }}>
          📊 Nombre de matières par abonnement
        </h5>
        <div className="loading-spinner" style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "200px",
          color: "#37A7DF"
        }}>
          <div className="spinner-border me-2" role="status" aria-hidden="true"></div>
          Chargement en cours...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="chart-container" style={{ padding: "20px" }}>
        <h5 className="chart-title" style={{ color: "#37A7DF", marginBottom: "20px" }}>
          📊 Nombre de matières par abonnement
        </h5>
        <div className="alert alert-danger" role="alert" style={{
          backgroundColor: "#fff",
          borderLeft: "4px solid #dc3545",
          borderRadius: "0 4px 4px 0"
        }}>
          <i className="fa fa-exclamation-triangle me-2"></i>
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => window.location.reload()}
          style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
        >
          <i className="fa fa-refresh me-2"></i>
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="chart-container" style={{
      backgroundColor: "#F6F4EE",
      borderRadius: "15px",
      padding: "25px",
      boxShadow: "0 8px 25px rgba(0,0,0,0.12)",
      border: "1px solid #EEF9F5",
      position: "relative",
      overflow: "hidden"
    }}>
      {/* Background decoration */}
      <div style={{
        position: "absolute",
        top: "-50px",
        right: "-50px",
        width: "150px",
        height: "150px",
        background: "linear-gradient(135deg, #37A7DF20, #F2BC0020)",
        borderRadius: "50%",
        zIndex: 0
      }}></div>

      <div style={{ position: "relative", zIndex: 1 }}>
        <div style={{
          textAlign: "center",
          marginBottom: "25px",
          padding: "15px",
          backgroundColor: "#EEF9F5",
          borderRadius: "10px",
          border: "2px solid #37A7DF"
        }}>
          <h5 className="chart-title" style={{
            color: "#37A7DF",
            marginBottom: "8px",
            fontWeight: "700",
            fontSize: "20px",
            textShadow: "0 1px 2px rgba(0,0,0,0.1)"
          }}>
            📊 Répartition des Matières par Abonnement
          </h5>
          <p style={{
            color: "#1D1D1B",
            margin: "0",
            fontSize: "14px",
            fontStyle: "italic"
          }}>
            Analyse de la distribution des matières selon les types d'abonnement
          </p>
        </div>

      {data.length > 0 ? (
        <>
          <div style={{ marginBottom: "15px", textAlign: "center" }}>
            <small style={{ color: "#1D1D1B", fontStyle: "italic" }}>
              Total des abonnements: {data.length} |
              Abonnements avec matières: {data.filter(item => item.nombreMatieres > 0).length}
            </small>
          </div>

          {/* Show a message if all values are 0 */}
          {data.every(item => item.nombreMatieres === 0) && (
            <div className="alert alert-info" style={{
              backgroundColor: "#EEF9F5",
              borderLeft: "4px solid #37A7DF",
              borderRadius: "0 4px 4px 0",
              marginBottom: "20px"
            }}>
              <i className="fa fa-info-circle me-2" style={{ color: "#37A7DF" }}></i>
              <strong>Information :</strong> Aucune matière n'est encore associée aux abonnements.
              <br />
              <small>
                Pour associer des matières aux abonnements, rendez-vous dans la section
                <strong> Gestion des Matières</strong> et modifiez les matières existantes
                pour les associer aux abonnements appropriés.
              </small>
            </div>
          )}

          <ResponsiveContainer width="100%" height={450}>
            <BarChart
              data={data}
              margin={{ top: 30, right: 30, left: 20, bottom: 80 }}
            >
              <defs>
                <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#37A7DF" stopOpacity={0.8}/>
                  <stop offset="100%" stopColor="#248E39" stopOpacity={0.6}/>
                </linearGradient>
              </defs>
              <CartesianGrid
                strokeDasharray="3 3"
                stroke="#B7B7B7"
                opacity={0.3}
                vertical={false}
              />
              <XAxis
                dataKey="abonnement"
                tick={{
                  fill: "#1D1D1B",
                  fontSize: 12,
                  fontWeight: "500"
                }}
                axisLine={{ stroke: "#B7B7B7", strokeWidth: 2 }}
                tickLine={{ stroke: "#B7B7B7" }}
                angle={-45}
                textAnchor="end"
                height={80}
                interval={0}
              />
              <YAxis
                tick={{ fill: "#1D1D1B", fontSize: 12 }}
                axisLine={{ stroke: "#B7B7B7", strokeWidth: 2 }}
                tickLine={{ stroke: "#B7B7B7" }}
                domain={[0, Math.max(...data.map(d => d.nombreMatieres), 5)]}
                label={{
                  value: "Nombre de matières",
                  angle: -90,
                  position: "insideLeft",
                  style: {
                    textAnchor: 'middle',
                    fill: "#1D1D1B",
                    fontWeight: "500",
                    fontSize: "14px"
                  }
                }}
              />
              <Tooltip
                formatter={(value) => [`${value} matière${value > 1 ? 's' : ''}`, "Total"]}
                labelFormatter={(label) => `Abonnement: ${label}`}
                contentStyle={{
                  backgroundColor: "#F6F4EE",
                  border: "2px solid #37A7DF",
                  borderRadius: "8px",
                  color: "#1D1D1B",
                  boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                  fontWeight: "500"
                }}
                labelStyle={{
                  color: "#000080",
                  fontWeight: "600"
                }}
                cursor={{ fill: "#EEF9F5", opacity: 0.3 }}
              />
              <Legend
                wrapperStyle={{
                  color: "#1D1D1B",
                  fontWeight: "500",
                  paddingTop: "20px"
                }}
              />
              <Bar
                dataKey="nombreMatieres"
                fill="url(#barGradient)"
                name="Nombre de matières"
                radius={[8, 8, 0, 0]}
                stroke="#37A7DF"
                strokeWidth={2}
              >
                <LabelList
                  dataKey="nombreMatieres"
                  position="top"
                  fill="#000080"
                  fontWeight="bold"
                  fontSize={14}
                  offset={10}
                />
              </Bar>
            </BarChart>
          </ResponsiveContainer>

          {/* Show summary statistics */}
          <div style={{
            marginTop: "20px",
            padding: "20px",
            background: "linear-gradient(135deg, #EEF9F5, #F6F4EE)",
            borderRadius: "12px",
            border: "1px solid #B7B7B7",
            display: "flex",
            justifyContent: "space-around",
            flexWrap: "wrap",
            boxShadow: "0 4px 15px rgba(0,0,0,0.08)"
          }}>
            <div style={{
              textAlign: "center",
              margin: "10px",
              padding: "15px",
              backgroundColor: "#F6F4EE",
              borderRadius: "8px",
              border: "2px solid #37A7DF",
              minWidth: "120px",
              boxShadow: "0 2px 8px rgba(55, 167, 223, 0.2)"
            }}>
              <div style={{
                fontSize: "28px",
                fontWeight: "bold",
                color: "#37A7DF",
                marginBottom: "5px"
              }}>
                {data.reduce((sum, item) => sum + item.nombreMatieres, 0)}
              </div>
              <small style={{
                color: "#1D1D1B",
                fontWeight: "500",
                textTransform: "uppercase",
                letterSpacing: "0.5px"
              }}>
                Total Matières
              </small>
            </div>

            <div style={{
              textAlign: "center",
              margin: "10px",
              padding: "15px",
              backgroundColor: "#F6F4EE",
              borderRadius: "8px",
              border: "2px solid #248E39",
              minWidth: "120px",
              boxShadow: "0 2px 8px rgba(36, 142, 57, 0.2)"
            }}>
              <div style={{
                fontSize: "28px",
                fontWeight: "bold",
                color: "#248E39",
                marginBottom: "5px"
              }}>
                {data.filter(item => item.nombreMatieres > 0).length}
              </div>
              <small style={{
                color: "#1D1D1B",
                fontWeight: "500",
                textTransform: "uppercase",
                letterSpacing: "0.5px"
              }}>
                Abonnements Actifs
              </small>
            </div>

            <div style={{
              textAlign: "center",
              margin: "10px",
              padding: "15px",
              backgroundColor: "#F6F4EE",
              borderRadius: "8px",
              border: "2px solid #F2BC00",
              minWidth: "120px",
              boxShadow: "0 2px 8px rgba(242, 188, 0, 0.2)"
            }}>
              <div style={{
                fontSize: "28px",
                fontWeight: "bold",
                color: "#F2BC00",
                marginBottom: "5px"
              }}>
                {data.length > 0 ? Math.round((data.reduce((sum, item) => sum + item.nombreMatieres, 0) / data.length) * 10) / 10 : 0}
              </div>
              <small style={{
                color: "#1D1D1B",
                fontWeight: "500",
                textTransform: "uppercase",
                letterSpacing: "0.5px"
              }}>
                Moyenne/Abonnement
              </small>
            </div>
          </div>
        </>
      ) : (
        <div className="no-data" style={{
          textAlign: "center",
          padding: "50px",
          color: "#B7B7B7",
          backgroundColor: "#EEF9F5",
          borderRadius: "12px",
          border: "2px dashed #B7B7B7"
        }}>
          <div style={{
            fontSize: "64px",
            marginBottom: "20px",
            color: "#37A7DF",
            opacity: 0.6
          }}>
            📊
          </div>
          <h6 style={{
            margin: "0 0 10px 0",
            fontSize: "18px",
            color: "#1D1D1B",
            fontWeight: "600"
          }}>
            Aucune donnée disponible
          </h6>
          <p style={{
            margin: "0",
            color: "#B7B7B7",
            fontSize: "14px"
          }}>
            Les données des matières par abonnement ne sont pas encore disponibles.
          </p>
        </div>
      )}
      </div>
    </div>
  );
};

export default MatieresParAbonnementChart;