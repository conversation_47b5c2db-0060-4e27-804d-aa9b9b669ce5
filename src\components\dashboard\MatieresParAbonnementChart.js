import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  LabelList,
} from "recharts";

const MatieresParAbonnementChart = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Try the statistics endpoint first
        let res;
        try {
          res = await axiosInstance.get("/api/statistics/matieres-par-abonnement");
          console.log("API Response from statistics:", res.data);
        } catch (statsError) {
          console.log("Statistics endpoint failed, trying alternative approach...");
          console.log("Stats error details:", statsError.response?.status, statsError.response?.data);

          // Alternative: Get all abonnements and count their matieres
          try {
            const abonnementsRes = await axiosInstance.get("/api/abonnements/all");
            const abonnements = abonnementsRes.data;
            console.log("Abonnements data:", abonnements);

            // If abonnements is paginated, handle it
            const abonnementsList = Array.isArray(abonnements) ? abonnements :
                                   (abonnements.content ? abonnements.content : []);

            if (abonnementsList.length === 0) {
              console.log("No abonnements found, trying to get matieres directly...");

              // Try to get matieres and group by abonnement
              try {
                const matieresRes = await axiosInstance.get("/api/matieres/all");
                const matieres = Array.isArray(matieresRes.data) ? matieresRes.data :
                               (matieresRes.data.content ? matieresRes.data.content : []);

                console.log("Matieres data:", matieres);

                // Group matieres by abonnement
                const abonnementGroups = {};
                matieres.forEach(matiere => {
                  if (matiere.abonnementIds && Array.isArray(matiere.abonnementIds)) {
                    matiere.abonnementIds.forEach(abonnement => {
                      const abonnementName = abonnement.nom || abonnement.name || `Abonnement ${abonnement.id}`;
                      if (!abonnementGroups[abonnementName]) {
                        abonnementGroups[abonnementName] = 0;
                      }
                      abonnementGroups[abonnementName]++;
                    });
                  }
                });

                const matieresCount = Object.entries(abonnementGroups).map(([nom, count]) => ({
                  abonnement: nom,
                  nombreMatieres: count
                }));

                res = { data: matieresCount };
                console.log("Matieres-based data created:", matieresCount);
              } catch (matieresError) {
                console.error("Matieres approach failed:", matieresError);
                // Create sample data for demonstration
                res = { data: [
                  { abonnement: "Basic", nombreMatieres: 0 },
                  { abonnement: "Standard", nombreMatieres: 0 },
                  { abonnement: "Premium", nombreMatieres: 0 }
                ]};
                console.log("Using sample data");
              }
            } else {
              // Count matieres for each abonnement
              const matieresCount = abonnementsList.map(abonnement => {
                let nombreMatieres = 0;

                // Try different possible property names for matieres
                if (abonnement.matieres && Array.isArray(abonnement.matieres)) {
                  nombreMatieres = abonnement.matieres.length;
                } else if (abonnement.matieresIds && Array.isArray(abonnement.matieresIds)) {
                  nombreMatieres = abonnement.matieresIds.length;
                } else if (abonnement.nombreMatieres) {
                  nombreMatieres = abonnement.nombreMatieres;
                }

                return {
                  abonnement: abonnement.nom || abonnement.name || abonnement.type || `Abonnement ${abonnement.id}`,
                  nombreMatieres: nombreMatieres
                };
              });

              res = { data: matieresCount };
              console.log("Alternative data created:", matieresCount);
            }
          } catch (altError) {
            console.error("Alternative approach also failed:", altError);
            throw new Error("Impossible de récupérer les données des matières par abonnement");
          }
        }

        // Handle different possible data structures
        let rawData = res.data;
        if (!Array.isArray(rawData)) {
          if (rawData.content && Array.isArray(rawData.content)) {
            rawData = rawData.content;
          } else if (rawData.data && Array.isArray(rawData.data)) {
            rawData = rawData.data;
          } else {
            console.error("Unexpected data structure:", rawData);
            throw new Error("Structure de données inattendue");
          }
        }

        // Transform data to match expected format
        const formattedData = rawData.map((item, index) => {
          // Handle different possible property names
          const abonnementName = item.abonnement ||
                                 item.abonnementNom ||
                                 item.nom ||
                                 item.name ||
                                 item.typeAbonnement ||
                                 `Abonnement ${index + 1}`;

          const nombreMatieres = item.nombreMatieres ||
                                 item.count ||
                                 item.total ||
                                 item.matieresCount ||
                                 0;

          return {
            abonnement: abonnementName,
            nombreMatieres: parseInt(nombreMatieres) || 0
          };
        });

        console.log("Formatted data:", formattedData);

        // Filter out entries with 0 matieres if needed
        const filteredData = formattedData.filter(item => item.nombreMatieres > 0);

        setData(filteredData.length > 0 ? filteredData : formattedData);
        setError(null);

      } catch (error) {
        console.error("Erreur lors du chargement des données :", error);
        setError(error.message || error.response?.data?.message || "Erreur de chargement des données");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);


  if (loading) {
    return (
      <div className="chart-container" style={{ padding: "20px", textAlign: "center" }}>
        <h5 className="chart-title" style={{ color: "#37A7DF", marginBottom: "20px" }}>
          📊 Nombre de matières par abonnement
        </h5>
        <div className="loading-spinner" style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "200px",
          color: "#37A7DF"
        }}>
          <div className="spinner-border me-2" role="status" aria-hidden="true"></div>
          Chargement en cours...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="chart-container" style={{ padding: "20px" }}>
        <h5 className="chart-title" style={{ color: "#37A7DF", marginBottom: "20px" }}>
          📊 Nombre de matières par abonnement
        </h5>
        <div className="alert alert-danger" role="alert" style={{
          backgroundColor: "#fff",
          borderLeft: "4px solid #dc3545",
          borderRadius: "0 4px 4px 0"
        }}>
          <i className="fa fa-exclamation-triangle me-2"></i>
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => window.location.reload()}
          style={{ backgroundColor: "#37A7DF", borderColor: "#37A7DF" }}
        >
          <i className="fa fa-refresh me-2"></i>
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="chart-container" style={{
      backgroundColor: "#F6F4EE",
      borderRadius: "10px",
      padding: "20px",
      boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
    }}>
      <h5 className="chart-title" style={{
        color: "#37A7DF",
        marginBottom: "20px",
        fontWeight: "600",
        textAlign: "center"
      }}>
        📊 Nombre de matières par abonnement
      </h5>

      {data.length > 0 ? (
        <>
          <div style={{ marginBottom: "15px", textAlign: "center" }}>
            <small style={{ color: "#1D1D1B", fontStyle: "italic" }}>
              Total des abonnements avec matières: {data.length}
            </small>
          </div>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={data}
              layout="vertical"
              margin={{ top: 20, right: 50, left: 80, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#B7B7B7" opacity={0.5} />
              <XAxis
                type="number"
                tick={{ fill: "#1D1D1B", fontSize: 12 }}
                label={{
                  value: "Nombre de matières",
                  position: "insideBottom",
                  offset: -5,
                  style: { textAnchor: 'middle', fill: "#1D1D1B" }
                }}
              />
              <YAxis
                dataKey="abonnement"
                type="category"
                width={120}
                tick={{ fill: "#1D1D1B", fontSize: 11 }}
              />
              <Tooltip
                formatter={(value) => [`${value} matière${value > 1 ? 's' : ''}`, "Total"]}
                labelFormatter={(label) => `Abonnement: ${label}`}
                contentStyle={{
                  backgroundColor: "#EEF9F5",
                  border: "1px solid #37A7DF",
                  borderRadius: "6px",
                  color: "#1D1D1B"
                }}
              />
              <Legend
                wrapperStyle={{ color: "#1D1D1B" }}
              />
              <Bar
                dataKey="nombreMatieres"
                fill="#248E39"
                name="Nombre de matières"
                radius={[0, 4, 4, 0]}
              >
                <LabelList
                  dataKey="nombreMatieres"
                  position="right"
                  fill="#000080"
                  fontWeight="bold"
                  fontSize={12}
                />
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </>
      ) : (
        <div className="no-data" style={{
          textAlign: "center",
          padding: "40px",
          color: "#B7B7B7"
        }}>
          <i className="fa fa-chart-bar" style={{ fontSize: "48px", marginBottom: "15px" }}></i>
          <p style={{ margin: "0", fontSize: "16px" }}>Aucune donnée disponible</p>
          <small>Les données des matières par abonnement ne sont pas encore disponibles.</small>
        </div>
      )}
    </div>
  );
};

export default MatieresParAbonnementChart;