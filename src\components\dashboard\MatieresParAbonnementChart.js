import React, { useEffect, useState } from "react";
import axiosInstance from "../../services/axiosService";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  LabelList,
} from "recharts";

const MatieresParAbonnementChart = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await axiosInstance.get("/api/statistics/matieres-par-abonnement");
        console.log("API Response:", res.data); // Debug log
        
        // Transform data to match expected format
        const formattedData = res.data.map(item => ({
          abonnement: item.abonnement, // Ensure this matches your DTO property
          nombreMatieres: item.nombreMatieres // Ensure this matches your DTO property
        }));
        
        setData(formattedData);
        console.log("Transformed data:", formattedData);

        setError(null);
      } catch (error) {
        console.error("Erreur lors du chargement des données :", error);
        setError(error.response?.data?.message || "Erreur de chargement des données");
      } finally {
        setLoading(false);
      }

    };
    fetchData();
  }, []);
  

  if (loading) {
    return <div className="loading-spinner">Chargement en cours...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="chart-container">
      <h5 className="chart-title">📊 Nombre de matières par abonnement</h5>

      {data.length > 0 ? (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={data}
            layout="vertical"
            margin={{ top: 20, right: 30, left: 60, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              type="number" 
              label={{ value: "Nombre de matières", position: "insideBottom", offset: -5 }} 
            />
            <YAxis
              dataKey="abonnement"
              type="category"
              width={120}
            />
            <Tooltip 
              formatter={(value) => [`${value} matières`, "Total"]}
              labelFormatter={(label) => `Abonnement: ${label}`}
            />
            <Legend />
            <Bar
              dataKey="nombreMatieres"
              fill="#248E39"
              name="Nombre de matières"
              radius={[8, 8, 8, 8]}
            >
              <LabelList 
                dataKey="nombreMatieres" 
                position="right" 
                fill="#000080" 
                fontWeight="bold" 
              />
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      ) : (
        <div className="no-data">Aucune donnée disponible</div>
      )}
    </div>
  );
};

export default MatieresParAbonnementChart;