import React from 'react';

const Apropos = () => {
  return (
    <div className="content-body">
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>About Student</h4>
            </div>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <ol className="breadcrumb">
              <li className="breadcrumb-item"><a href="javascript:void(0);">Students</a></li>
              <li className="breadcrumb-item active"><a href="javascript:void(0);">About Student</a></li>
            </ol>
          </div>
        </div>
        
        <div className="row">
          <div className="col-xl-3 col-xxl-4 col-lg-4">
            <div className="row">
              <div className="col-lg-12">
                <div className="card">
                  <div className="text-center p-3 overlay-box" style={{ backgroundImage: 'url(images/big/img1.jpg)' }}>
                    <div className="profile-photo">
                      <img src="images/profile/profile.png" width="100" className="img-fluid rounded-circle" alt="" />
                    </div>
                    <h3 className="mt-3 mb-1 text-white">Deangelo Sena</h3>
                  </div>
                  <ul className="list-group list-group-flush">
                    <li className="list-group-item d-flex justify-content-between"><span className="mb-0">Followers</span> <strong className="text-muted">1204</strong></li>
                    <li className="list-group-item d-flex justify-content-between"><span className="mb-0">Following</span> <strong className="text-muted">2540</strong></li>
                    <li className="list-group-item d-flex justify-content-between"><span className="mb-0">Friends</span> <strong className="text-muted">2540</strong></li>
                  </ul>
                  <div className="card-footer text-center border-0 mt-0">                                
                    <a href="javascript:void(0);" className="btn btn-primary px-4">Follow</a>
                    <a href="javascript:void(0);" className="btn btn-warning px-4">Message</a>
                  </div>
                </div>
              </div>
              <div className="col-lg-12">
                <div className="card overflow-hidden">
                  <div className="card-header">
                    <h2 className="card-title">About Me</h2>
                  </div>
                  <div className="card-body pb-0">
                    <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry.</p>
                    <ul className="list-group list-group-flush">
                      <li className="list-group-item d-flex px-0 justify-content-between">
                        <strong>Gender</strong>
                        <span className="mb-0">Male</span>
                      </li>
                      <li className="list-group-item d-flex px-0 justify-content-between">
                        <strong>Education</strong>
                        <span className="mb-0">PHD</span>
                      </li>
                      <li className="list-group-item d-flex px-0 justify-content-between">
                        <strong>Email</strong>
                        <span className="mb-0"><EMAIL></span>
                      </li>
                      <li className="list-group-item d-flex px-0 justify-content-between">
                        <strong>Phone</strong>
                        <span className="mb-0">+01 ************</span>
                      </li>
                    </ul>
                  </div>
                  <div className="card-footer pt-0 pb-0 text-center">
                    <div className="row">
                      <div className="col-4 pt-3 pb-3 border-end">
                        <h3 className="mb-1 text-primary">150</h3>
                        <span>Projects</span>
                      </div>
                      <div className="col-4 pt-3 pb-3 border-end">
                        <h3 className="mb-1 text-primary">140</h3>
                        <span>Uploads</span>
                      </div>
                      <div className="col-4 pt-3 pb-3">
                        <h3 className="mb-1 text-primary">45</h3>
                        <span>Tasks</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-xl-9 col-xxl-8 col-lg-8">
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-body">
                    <div className="profile-tab">
                      <div className="custom-tab-1">
                        <ul className="nav nav-tabs">
                          <li className="nav-item"><a href="#my-posts" data-bs-toggle="tab" className="nav-link active show">Posts</a></li>
                          <li className="nav-item"><a href="#about-me" data-bs-toggle="tab" className="nav-link">About Me</a></li>
                        </ul>
                        <div className="tab-content">
                          <div id="about-me" className="tab-pane fade">
                            <div className="profile-about-me">
                              <div className="pt-4 border-bottom-1 pb-4">
                                <p>A wonderful serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with my whole heart. I am alone, and feel the charm of existence was created for the bliss of souls like mine.</p>
                              </div>
                            </div>
                            <div className="profile-skills pt-2 border-bottom-1 pb-2">
                              <h4 className="text-primary mb-4">Skills</h4>
                              <a href="javascript:void(0);" className="btn btn-outline-dark btn-rounded px-4 me-2 mb-2">Admin</a>
                              <a href="javascript:void(0);" className="btn btn-outline-dark btn-rounded px-4 me-2 mb-2">Dashboard</a>
                              <a href="javascript:void(0);" className="btn btn-outline-dark btn-rounded px-4 me-2 mb-2">Photoshop</a>
                            </div>
                          </div>
                          <div id="my-posts" className="tab-pane fade active show">
                            <div className="my-post-content pt-3">
                              <div className="post-input">
                                <textarea name="textarea" id="textarea" cols="30" rows="5" className="form-control bg-transparent" placeholder="Please type what you want...."></textarea> 
                                <a href="javascript:void(0);" className="btn btn-primary light me-1 px-3" data-bs-toggle="modal" data-bs-target="#linkModal"><i className="fa fa-link m-0"></i> </a>
                                {/* Modal */}
                                <div className="modal fade" id="linkModal">
                                  <div className="modal-dialog modal-dialog-centered" role="document">
                                    <div className="modal-content">
                                      <div className="modal-header">
                                        <h5 className="modal-title">Social Links</h5>
                                        <button type="button" className="btn-close" data-bs-dismiss="modal"></button>
                                      </div>
                                      <div className="modal-body">
                                        <a className="btn-social facebook" href="javascript:void(0)"><i className="fab fa-facebook-f"></i></a>
                                        <a className="btn-social google-plus" href="javascript:void(0)"><i className="fab fa-google-plus-g"></i></a>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Apropos;
