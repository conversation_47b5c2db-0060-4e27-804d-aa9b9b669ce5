import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosService";

const AjouterChapitre = () => {
  const navigate = useNavigate();
  const [niveaux, setNiveaux] = useState([]);
  const [matieres, setMatieres] = useState([]);
  const [formData, setFormData] = useState({
    nomChapitre: "", // Nom du Chapitre
    matiereId: "",
    nomDeProf: "",
    duree: "",
    nombreDeCours: "",
    niveauId: "",
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState(""); // Message de succès

  // Charger la liste des matières et niveaux au chargement du composant
  useEffect(() => {
    axiosInstance
      .get("/api/matieres")
      .then((response) => setMatieres(response.data))
      .catch((error) =>
        console.error("Erreur de récupération des matières:", error)
      );
  }, []);

  useEffect(() => {
    axiosInstance
      .get("/api/niveaux/all")
      .then((response) => setNiveaux(response.data))
      .catch((error) =>
        console.error("Erreur de récupération des niveaux:", error)
      );
  }, []);

  // Gérer les changements dans le formulaire
  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [id]: value,
    }));

    setErrors((prevErrors) => ({
      ...prevErrors,
      [id]: "",
    }));
  };

  // Validation du formulaire
  const validateForm = () => {
    let newErrors = {};
    if (!formData.nomChapitre.trim())
      newErrors.nomChapitre = "Le nom du chapitre est requis";
    if (!formData.matiereId) newErrors.matiereId = "La matière est requise";
    if (!formData.niveauId) newErrors.niveauId = "Le niveau est requis";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Soumettre le formulaire
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        const response = await axiosInstance.post(
          `/api/chapitres/${formData.matiereId}/${formData.niveauId}`,
          {
            nomChapitre: formData.nomChapitre,
            nomDeProf: formData.nomDeProf,
            duree: parseInt(formData.duree, 10),
            nombreDeCours: parseInt(formData.nombreDeCours, 10),
            matiere: { idMatiere: formData.matiereId },
            niveau: { idNiveau: formData.niveauId },
          }
        );
        setSuccessMessage("Chapitre ajouté avec succès !");
        setTimeout(() => {
          navigate("/chapitres");
        }, 2000);
      } catch (error) {
        console.error("Erreur lors de l'ajout du chapitre:", error);
        setErrors({ submit: "Erreur lors de l'ajout du chapitre" });
      }
    }
  };

  return (
    <div className="container-fluid">
      <div className="row page-titles mx-0">
        <div className="col-sm-6 p-md-0">
          <h4 style={{ color: "#37A7DF" }}>Ajouter un Chapitre</h4>
        </div>
        <div className="col-sm-6 p-md-0 d-flex justify-content-end">
          <ol className="breadcrumb">
            <li className="breadcrumb-item">
              <Link
                to="/AfficherChapitre"
                className="nav-text"
                style={{ color: "#37A7DF" }}
              >
                Chapitre
              </Link>
            </li>
            <li className="breadcrumb-item active">
              <a href="#" style={{ color: "#37A7DF" }}>
                Ajouter
              </a>
            </li>
          </ol>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-12">
          <div className="card shadow-lg p-4">
            <div
              className="card-header"
              style={{ backgroundColor: "#EEF9F5", color: "black" }}
            >
              <h4 className="card-title">Détails du Chapitre</h4>
            </div>
            <div className="card-body">
              {successMessage && (
                <div className="alert alert-success d-flex justify-content-between">
                  {successMessage}
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setSuccessMessage("")}
                  ></button>
                </div>
              )}

              <form onSubmit={handleSubmit}>
                <div className="row">
                  <div className="col-sm-6 mb-3">
                    <label className="form-label" htmlFor="nomChapitre">
                      Nom du Chapitre
                    </label>
                    <input
                      id="nomChapitre"
                      type="text"
                      className={`form-control ${
                        errors.nomChapitre ? "is-invalid" : ""
                      }`}
                      value={formData.nomChapitre}
                      onChange={handleChange}
                    />
                    {errors.nomChapitre && (
                      <small className="text-danger">
                        {errors.nomChapitre}
                      </small>
                    )}
                  </div>

                  <div className="col-sm-6 mb-3">
                    <label className="form-label" htmlFor="nomDeProf">
                      Nom du Professeur
                    </label>
                    <input
                      id="nomDeProf"
                      type="text"
                      className={`form-control ${
                        errors.nomDeProf ? "is-invalid" : ""
                      }`}
                      value={formData.nomDeProf}
                      onChange={handleChange}
                    />
                    {errors.nomDeProf && (
                      <small className="text-danger">{errors.nomDeProf}</small>
                    )}
                  </div>

                  <div className="col-sm-6 mb-3">
                    <label className="form-label" htmlFor="duree">
                      Durée (en heures)
                    </label>
                    <input
                      id="duree"
                      type="number"
                      className={`form-control ${
                        errors.duree ? "is-invalid" : ""
                      }`}
                      value={formData.duree}
                      onChange={handleChange}
                    />
                    {errors.duree && (
                      <small className="text-danger">{errors.duree}</small>
                    )}
                  </div>

                  <div className="col-sm-6 mb-3">
                    <label className="form-label" htmlFor="nombreDeCours">
                      Nombre de Cours
                    </label>
                    <input
                      id="nombreDeCours"
                      type="number"
                      className={`form-control ${
                        errors.nombreDeCours ? "is-invalid" : ""
                      }`}
                      value={formData.nombreDeCours}
                      onChange={handleChange}
                    />
                    {errors.nombreDeCours && (
                      <small className="text-danger">
                        {errors.nombreDeCours}
                      </small>
                    )}
                  </div>

                  <div className="col-sm-6 mb-3">
                    <label className="form-label" htmlFor="matiereId">
                      Choisir une Matière
                    </label>
                    <select
                      id="matiereId"
                      className={`form-control ${
                        errors.matiereId ? "is-invalid" : ""
                      }`}
                      value={formData.matiereId}
                      onChange={handleChange}
                    >
                      <option value="">Sélectionner une matière</option>
                      {matieres.map((matiere) => (
                        <option
                          key={matiere.idMatiere}
                          value={matiere.idMatiere}
                        >
                          {matiere.nomMatiere}
                        </option>
                      ))}
                    </select>
                    {errors.matiereId && (
                      <small className="text-danger">{errors.matiereId}</small>
                    )}
                  </div>

                  <div className="col-sm-6 mb-3">
                    <label className="form-label" htmlFor="niveauId">
                      Choisir un Niveau
                    </label>
                    <select
                      id="niveauId"
                      className="form-control"
                      value={formData.niveauId}
                      onChange={handleChange}
                    >
                      <option value="">Sélectionner un niveau</option>
                      {niveaux.map((niveau) => (
                        <option key={niveau.id} value={niveau.id}>
                          {niveau.nom}
                        </option>
                      ))}
                    </select>
                    {errors.niveauId && (
                      <small className="text-danger">{errors.niveauId}</small>
                    )}
                  </div>

                  <div className="col-12">
                    <button
                      type="submit"
                      className="btn btn-primary mt-6"
                      style={{
                        backgroundColor: "#37A7DF",
                        borderColor: "#37A7DF",
                        color: "#fff",
                      }}
                    >
                      Ajouter
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AjouterChapitre;
