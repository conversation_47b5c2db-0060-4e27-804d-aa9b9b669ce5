/* Palette moderne */
body {
  font-family: 'Poppins', sans-serif;
  color: #fff;
}

/* <PERSON>ffet Glassmorphism */
.card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}
.card:hover {
  transform: translateY(-5px);
}

/* Bouton moderne */
.btn-primary {
  background-color: #37A7DF;
  border: none;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease-in-out;
}
.btn-primary:hover {
  background-color: #2563eb;
  box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.4);
  transform: translateY(-2px);
}

/* Animation subtile sur les entrées */
input, select {
  transition: all 0.3s ease-in-out;
  border: 2px solid transparent;
  padding: 10px;
  border-radius: 6px;
}
input:focus, select:focus {
  border: 2px solid #37A7DF;
  box-shadow: 0px 0px 8px rgba(55, 167, 223, 0.5);
}

/* Effet de flou sur la section */
.container-fluid {
  padding: 20px;
  backdrop-filter: blur(5px);
}
