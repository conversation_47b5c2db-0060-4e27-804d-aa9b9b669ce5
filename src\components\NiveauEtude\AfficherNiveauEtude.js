import React from 'react';
import { useNavigate } from 'react-router-dom';

const AfficherNiveauEtude = () => {
  const navigate = useNavigate();

  return (
    <div className="content-body">
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <h4 style={{ color: "#37A7DF" }}>Niveaux d'Études</h4>
          </div>
          <div className="col-sm-6 p-md-0 justify-content-sm-end mt-2 mt-sm-0 d-flex">
            <button
              className="btn btn-primary"
              style={{
                backgroundColor: "#37A7DF",
                borderColor: "#37A7DF",
                color: "#fff",
              }}
              onClick={() => navigate("/AjouterNiveauEtude")}
            >
              + Ajouter un Niveau
            </button>
          </div>
        </div>
        <div className="row">
          {/* Study level content will be implemented here */}
          <div className="col-12">
            <p>Implémentation à venir...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AfficherNiveauEtude;