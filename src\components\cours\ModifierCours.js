import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axiosInstance from "../../services/axiosService";

const ModifierCours = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  
  const [formData, setFormData] = useState({
    titre: "",
    description: "",
    duree: "",
    dateCreation: "",
    lien: "",
    chapitre: null
  });

  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (id) {
      axiosInstance.get(`/api/cours/${id}`)
        .then(response => {
          setFormData({
            titre: response.data.titre || "",
            description: response.data.description || "",
            duree: response.data.duree || "",
            dateCreation: response.data.dateCreation ? response.data.dateCreation.split("T")[0] : "",
            lien: response.data.lien || "",
            chapitre: response.data.chapitre
          });
        })
        .catch(err => setError(err.message));
    }
  }, [id]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    try {
      await axiosInstance.put(`/api/cours/${id}`, formData);
      setSuccess(true);
      setTimeout(() => navigate("/cours"), 2000);
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div className="content-body">
      <div className="container-fluid">
        <div className="row page-titles mx-0">
          <div className="col-sm-6 p-md-0">
            <div className="welcome-text">
              <h4>Modifier le cours</h4>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-xl-12 col-lg-12">
            <div className="card">
              <div className="card-header">
                <h4 className="card-title">Détails du cours</h4>
              </div>
              <div className="card-body">
                {error && (
                  <div className="alert alert-danger" role="alert">
                    {error}
                  </div>
                )}
                {success && (
                  <div className="alert alert-success" role="alert">
                    Cours modifié avec succès! Redirection...
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="row">
                    <div className="col-sm-6 mb-3">
                      <div className="form-group">
                        <label className="form-label">Titre</label>
                        <input
                          type="text"
                          className="form-control"
                          name="titre"
                          value={formData.titre}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                    </div>

                    <div className="col-sm-6 mb-3">
                      <div className="form-group">
                        <label className="form-label">Durée</label>
                        <input
                          type="text"
                          className="form-control"
                          name="duree"
                          value={formData.duree}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    <div className="col-sm-12 mb-3">
                      <div className="form-group">
                        <label className="form-label">Description</label>
                        <textarea
                          className="form-control"
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          rows="4"
                        ></textarea>
                      </div>
                    </div>

                    <div className="col-sm-6 mb-3">
                      <div className="form-group">
                        <label className="form-label">Lien</label>
                        <input
                          type="url"
                          className="form-control"
                          name="lien"
                          value={formData.lien}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    <div className="col-sm-6 mb-3">
                      <div className="form-group">
                        <label className="form-label">Date de création</label>
                        <input
                          type="date"
                          className="form-control"
                          name="dateCreation"
                          value={formData.dateCreation}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="form-group">
                    <button type="submit" className="btn btn-primary">
                      Enregistrer les modifications
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModifierCours;
