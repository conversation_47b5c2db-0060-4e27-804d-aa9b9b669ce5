import React, { useEffect, useState } from "react";
import axios from "axios";
import "./dashboard.css";
import axiosInstance from "../../services/axiosService";

const Chart = () => {
  const [stats, setStats] = useState({
    totalMatieres: 0,
    totalAbonnements: 0,
    totalNiveaux: 0,
    totalCours: 0,
    totalChapitres: 0,
    totalEtudiants: 0,
    totalEnseignants: 0,
  });
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await axiosInstance.get("/api/statistics");
        setStats(response.data);
      } catch (err) {
        setError("Erreur lors du chargement des statistiques.");
      }
    };
    fetchStats();
  }, []);

  return (
    <div className="dashboard-container">
      <h2 className="dashboard-title">Tableau de Bord</h2>

      <div className="row">
        <div className="col-md-3">
          <div className="stat-card matieres">
            <div className="card-body">
              <h6 className="stat-title">📚 Total Matières</h6>
              <h3 className="stat-value">{stats.totalMatieres}</h3>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="stat-card abonnements">
            <div className="card-body">
              <h6 className="stat-title">💳 Total Abonnements</h6>
              <h3 className="stat-value">{stats.totalAbonnements}</h3>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="stat-card niveaux">
            <div className="card-body">
              <h6 className="stat-title">📊 Total Niveaux</h6>
              <h3 className="stat-value">{stats.totalNiveaux}</h3>
            </div>
          </div>
        </div>

        <div className="col-md-3">
          <div className="stat-card cours">
            <div className="card-body">
              <h6 className="stat-title">🎓 Total Cours</h6>
              <h3 className="stat-value">{stats.totalCours}</h3>
            </div>
          </div>
        </div>
      </div>

      <div className="row mt-4">
        <div className="col-md-4">
          <div className="stat-card chapitres">
            <div className="card-body">
              <h6 className="stat-title">📖 Total Chapitres</h6>
              <h3 className="stat-value">{stats.totalChapitres}</h3>
            </div>
          </div>
        </div>

        <div className="col-md-4">
          <div className="stat-card etudiants">
            <div className="card-body">
              <h6 className="stat-title">👨‍🎓 Total Étudiants</h6>
              <h3 className="stat-value">{stats.totalEtudiants}</h3>
            </div>
          </div>
        </div>

        <div className="col-md-4">
          <div className="stat-card enseignants">
            <div className="card-body">
              <h6 className="stat-title">👩‍🏫 Total Enseignants</h6>
              <h3 className="stat-value">{stats.totalEnseignants}</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chart;
