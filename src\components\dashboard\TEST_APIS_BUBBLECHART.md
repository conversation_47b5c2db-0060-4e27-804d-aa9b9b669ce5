# 🧪 Test des APIs pour BubbleChart

## 🎯 APIs Utilisées par le BubbleChart

Le composant BubbleChart utilise deux endpoints API pour récupérer ses données :

### 1. API Statistiques (Existante) ✅
```
GET /api/statistics
```
**Utilisée pour :** Matières et Niveaux
**Status :** ✅ Déjà fonctionnelle (utilisée par d'autres composants)

### 2. API Étudiants (Nouvelle) ⚠️
```
GET /api/etudiants/count
```
**Utilisée pour :** Nombre d'étudiants
**Status :** ⚠️ Peut ne pas exister

## 🔍 Comment Tester les APIs

### Test Manuel dans le Navigateur

1. **Ouvrir les outils de développement** (F12)
2. **Aller dans l'onglet Console**
3. **Exécuter ces commandes :**

```javascript
// Test API Statistics (doit fonctionner)
fetch('/api/statistics')
  .then(response => response.json())
  .then(data => console.log('📊 Statistics:', data))
  .catch(error => console.error('❌ Error Statistics:', error));

// Test API Étudiants (peut échouer)
fetch('/api/etudiants/count')
  .then(response => response.json())
  .then(data => console.log('👥 Étudiants:', data))
  .catch(error => console.error('❌ Error Étudiants:', error));
```

### Test avec Postman/Insomnia

1. **GET** `http://localhost:8084/api/statistics`
2. **GET** `http://localhost:8084/api/etudiants/count`

## 📋 Résultats Attendus

### API Statistics (✅ Doit Fonctionner)
```json
{
  "totalCours": 25,
  "totalAbonnements": 3,
  "totalNiveaux": 5,
  "totalMatiere": 12
}
```

### API Étudiants (⚠️ Peut Échouer)
```json
{
  "totalEtudiants": 150
}
```

## 🛠️ Si l'API Étudiants N'Existe Pas

### Option 1 : Le Composant Gère Automatiquement ✅
Le BubbleChart est conçu pour fonctionner même si l'API étudiants n'existe pas :
- Il affichera **0 étudiants**
- Aucune erreur ne sera générée
- Les autres données (matières, niveaux) s'afficheront normalement

### Option 2 : Créer l'API Backend (Optionnel)
Si vous voulez afficher le vrai nombre d'étudiants, créez cet endpoint :

```java
// Dans votre contrôleur Spring Boot
@GetMapping("/etudiants/count")
public ResponseEntity<Map<String, Object>> getEtudiantsCount() {
    try {
        long totalEtudiants = etudiantService.count(); // ou votre méthode
        
        Map<String, Object> response = new HashMap<>();
        response.put("totalEtudiants", totalEtudiants);
        
        return ResponseEntity.ok(response);
    } catch (Exception e) {
        return ResponseEntity.status(500).build();
    }
}
```

### Option 3 : Modifier le Composant (Alternative)
Si vous préférez utiliser une API différente, modifiez le BubbleChart :

```javascript
// Dans BubbleChart.js, ligne ~150, remplacer :
axiosInstance.get("/api/etudiants/count")

// Par votre endpoint existant :
axiosInstance.get("/api/votre-endpoint-etudiants")
```

## 🧪 Test Complet du BubbleChart

### 1. Lancer l'Application
```bash
npm start
```

### 2. Ouvrir la Console du Navigateur
- Appuyer sur **F12**
- Aller dans l'onglet **Console**

### 3. Vérifier les Logs
Vous devriez voir ces messages :
```
📊 Données statistiques reçues: {totalCours: 25, totalAbonnements: 3, ...}
👥 Données étudiants reçues: {totalEtudiants: 150}
✅ Données formatées pour le graphique à bulles: [...]
```

### 4. Si l'API Étudiants Échoue
Vous verrez ce message (c'est normal) :
```
⚠️ API étudiants non disponible, utilisation de 0 par défaut
```

## 🔧 Dépannage

### Problème : Aucune bulle ne s'affiche
**Cause :** Toutes les valeurs sont à 0
**Solution :** Vérifier que l'API statistics retourne des données > 0

### Problème : Erreur 404 sur /api/statistics
**Cause :** L'API backend n'est pas démarrée
**Solution :** Démarrer le serveur backend Spring Boot

### Problème : Erreur CORS
**Cause :** Configuration CORS manquante
**Solution :** Vérifier la configuration CORS du backend

### Problème : Bulles trop petites
**Cause :** Valeurs très différentes (ex: 2 matières, 1000 étudiants)
**Solution :** Le composant normalise automatiquement les tailles

## ✅ Checklist de Test

- [ ] Backend Spring Boot démarré
- [ ] Frontend React démarré (`npm start`)
- [ ] Page dashboard accessible
- [ ] BubbleChart visible dans le dashboard
- [ ] Console sans erreurs critiques
- [ ] Tooltips fonctionnent au survol des bulles
- [ ] Cartes statistiques affichées sous le graphique
- [ ] Design responsive testé (mobile/desktop)

## 📊 Données de Test

Si vous voulez tester avec des données spécifiques, voici des exemples :

### Cas 1 : Données Équilibrées
```json
{
  "totalMatiere": 10,
  "totalNiveaux": 5,
  "totalEtudiants": 50
}
```
**Résultat :** Bulles de tailles similaires

### Cas 2 : Données Déséquilibrées
```json
{
  "totalMatiere": 2,
  "totalNiveaux": 3,
  "totalEtudiants": 500
}
```
**Résultat :** Bulle étudiants très grande, autres petites

### Cas 3 : Données Nulles
```json
{
  "totalMatiere": 0,
  "totalNiveaux": 0,
  "totalEtudiants": 0
}
```
**Résultat :** Message informatif affiché

## 🎯 Résumé

Le BubbleChart est conçu pour être **robuste et tolérant aux pannes** :
- ✅ Fonctionne même si l'API étudiants n'existe pas
- ✅ Gère les données nulles ou manquantes
- ✅ Affiche des messages informatifs appropriés
- ✅ Normalise automatiquement les tailles de bulles

**Vous pouvez l'utiliser immédiatement** même si toutes les APIs ne sont pas encore implémentées ! 🚀
